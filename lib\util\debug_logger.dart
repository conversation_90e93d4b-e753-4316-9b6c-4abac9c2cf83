import 'package:shared_preferences/shared_preferences.dart';

class DebugLogger {
  static const _key = 'debug_logs';

  /// Append a new log message (with timestamp)
  static Future<void> log(String message) async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now().toIso8601String();
    final entry = "[$now] $message";

    // Get existing logs
    final existingLogs = prefs.getStringList(_key) ?? [];

    // Keep only last 50 logs (avoid unlimited growth)
    if (existingLogs.length > 50) {
      existingLogs.removeRange(0, existingLogs.length - 50);
    }

    // Add new entry
    existingLogs.add(entry);
    await prefs.setStringList(_key, existingLogs);
  }

  /// Read all saved logs
  static Future<List<String>> readLogs() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_key) ?? [];
  }

  /// Clear all logs
  static Future<void> clear() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_key);
  }
}
