import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class PostAndProductImageWidgets extends StatefulWidget {
  final String localOrNetworkImage;
  final String? mediaObjectUrl;
  final double imageSize;
  final double paddingNextToTheImage;
  final bool isProduct;
  final Uint8List? webImageBytes;

  const PostAndProductImageWidgets({
    Key? key,
    required this.localOrNetworkImage,
    this.mediaObjectUrl,
    this.imageSize = 200,
    this.paddingNextToTheImage = 10,
    this.isProduct = false,
    this.webImageBytes,
  }) : super(key: key);

  @override
  State<PostAndProductImageWidgets> createState() =>
      _PostAndProductImageWidgetsState();
}

class _PostAndProductImageWidgetsState
    extends State<PostAndProductImageWidgets> {
  bool _isLocalFileFuture = false;
  double? _imageRatio; // width / height for local images
  bool _imageLoadError = false; // track if image loading failed
  ImageStreamListener? _imageListener; // Keep reference to listener

  @override
  void initState() {
    super.initState();
    checkIfLocalFile();
  }

  @override
  void dispose() {
    // Remove listener when widget is disposed
    if (_imageListener != null) {
      final imageProvider = FileImage(File(widget.localOrNetworkImage));
      final stream = imageProvider.resolve(const ImageConfiguration());
      stream.removeListener(_imageListener!);
    }
    super.dispose();
  }

  void checkIfLocalFile() async {
    if (kIsWeb) return;
    _isLocalFileFuture = await CommonMethods.isLocalFile(
      imagePath: widget.localOrNetworkImage,
    );

    // If it's a local file, calculate its aspect ratio
    if (_isLocalFileFuture) {
      _calculateLocalImageRatio();
    }

    setState(() {});
  }

  void _calculateLocalImageRatio() {
    if (!mounted) return;

    // Load the image to get its dimensions
    final imageProvider = FileImage(File(widget.localOrNetworkImage));
    final stream = imageProvider.resolve(const ImageConfiguration());

    void listener(ImageInfo info, bool synchronousCall) {
      final width = info.image.width;
      final height = info.image.height;
      if (mounted && height != 0) {
        setState(() {
          _imageRatio = width / height;
        });
      }
      // Remove the listener after getting the image info
      if (_imageListener != null) {
        stream.removeListener(_imageListener!);
        _imageListener = null;
      }
    }

    void errorHandler(Object exception, StackTrace? stackTrace) {
      if (mounted) {
        setState(() {
          _imageLoadError = true;
        });
      }
      // Remove the listener on error
      if (_imageListener != null) {
        stream.removeListener(_imageListener!);
        _imageListener = null;
      }
    }

    // Create and add the listener
    _imageListener = ImageStreamListener(listener, onError: errorHandler);
    stream.addListener(_imageListener!);
  }

  @override
  Widget build(BuildContext context) {
    // ---------------- Web image
    if (kIsWeb && widget.webImageBytes != null) {
      return Container(
        margin: EdgeInsets.only(right: widget.paddingNextToTheImage),
        child: clampedImage(
          Image.memory(
            widget.webImageBytes!,
            fit: BoxFit.cover,
          ),
          height: widget.imageSize,
          imageRatio: _imageRatio,
        ),
      );
    }

    // ---------------- Local file with error
    if (_isLocalFileFuture && _imageLoadError) {
      return Container(
        margin: EdgeInsets.only(right: widget.paddingNextToTheImage),
        child: clampedImage(
          Container(
            color: AppColors.lightGray,
            child: const Icon(Icons.image, size: 48),
          ),
          height: widget.imageSize,
        ),
      );
    }

    // ---------------- Local file
    if (_isLocalFileFuture) {
      return Container(
        child: clampedImage(
          Image.file(
            File(widget.localOrNetworkImage),
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              // Handle image loading errors
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  setState(() {
                    _imageLoadError = true;
                  });
                }
              });
              return Container(
                color: AppColors.lightGray,
                child: const Icon(Icons.image, size: 48),
              );
            },
          ),
          height: widget.imageSize,
          imageRatio: _imageRatio, // Use calculated ratio for local images
        ),
      );
    }

    // ---------------- Network image (priority: mediaObjectUrl > localOrNetworkImage)
    String imageUrl;
    if (widget.mediaObjectUrl != null && widget.mediaObjectUrl!.isNotEmpty) {
      imageUrl = widget.mediaObjectUrl!;
    } else if (widget.localOrNetworkImage.isNotEmpty) {
      imageUrl = widget.localOrNetworkImage.startsWith('/media/')
          ? "${AppConstants.baseUrl}${widget.localOrNetworkImage}"
          : "${AppConstants.baseUrl}${AppConstants.baseMediaUrlWithslashes}${widget.localOrNetworkImage}";
    } else {
      imageUrl = "";
    }

    return Container(
      margin: EdgeInsets.only(right: widget.paddingNextToTheImage),
      child: imageUrl.isNotEmpty
          ? NetworkImageWithAspectRatio(
              url: imageUrl,
              height: widget.imageSize,
            )
          : Container(
              width: widget.imageSize,
              height: widget.imageSize,
              color: AppColors.lightGray,
              child: Icon(
                Icons.image,
                color: AppColors.writingBlack1,
                size: 48,
              ),
            ),
    );
  }
}
