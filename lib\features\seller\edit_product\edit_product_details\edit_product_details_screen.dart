import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/seller/add_edit_product_fields/add_edit_product_fields.dart';
import 'package:swadesic/features/seller/add_image/selected_image_preview/selected_image_preview_screen.dart';
import 'package:swadesic/features/seller/add_product/add_product_common_widgets.dart';
import 'package:swadesic/features/seller/edit_product/edit_product_details/edit_product_details_bloc.dart';
import 'package:swadesic/features/seller/product_text_fields/product_text_fields_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/auto_hide_navigation_service.dart';
import 'dart:math' as math;
import 'package:swadesic/features/widgets/post_widgets/post_image.dart';

// region Edit Product Details
class EditProductDetailsScreen extends StatefulWidget {
  final int storeId;
  final List<String> productReferenceList;
  const EditProductDetailsScreen(
      {Key? key, required this.storeId, required this.productReferenceList})
      : super(key: key);

  @override
  _EditProductDetailsScreenState createState() =>
      _EditProductDetailsScreenState();
}
// endregion

class _EditProductDetailsScreenState extends State<EditProductDetailsScreen>
    with AutoHideNavigationMixin<EditProductDetailsScreen> {
  // region Bloc
  late EditProductDetailsBloc editProductDetailsBloc;

  // endregion

  // region Init
  @override
  void initState() {
    editProductDetailsBloc = EditProductDetailsBloc(
        context, widget.storeId, widget.productReferenceList);
    editProductDetailsBloc.init();
    // Force-hide bottom navigation as soon as this screen opens
    enableAutoHideNavigation();
    forceHideNavigationBars();
    super.initState();
  }

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
        appBar: appBar(),
        backgroundColor: AppColors.appWhite,
        body: SafeArea(child: body()),
      ),
    );
  }

  // endregion

  //region Dispose
  @override
  void dispose() {
    // Re-enable/show navigation when leaving this screen
    disableAutoHideNavigation();
    editProductDetailsBloc.dispose();
    super.dispose();
  }
  // endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: true,
        customTitleWidget: StreamBuilder<int>(
            stream: editProductDetailsBloc.editProductNumberCtrl.stream,
            initialData: editProductDetailsBloc.i,
            builder: (context, snapshot) {
              return snapshot.data! + 1 ==
                      editProductDetailsBloc.productReferenceList.length
                  ? AppCommonWidgets.appBarTitleText(
                      text: AppStrings.editProduct)
                  : AppCommonWidgets.appBarTitleText(
                      text:
                          "${AppStrings.editProduct} (${snapshot.data! + 1}/${editProductDetailsBloc.productReferenceList.length})");
            }),
        // titleWidget: Text(UserProfileBloc.getUserDetailsResponse.userDetail!.userName!??"", style: TextStyle(fontFamily: AppConstants.rRegular, fontSize: 19, fontWeight: FontWeight.w700, color: AppColors.appBlack)),
        isDefaultMenuVisible: false,
        isDropdownVisible: false,
        isCartVisible: false,
        isMembershipVisible: false,
        isTextButtonVisible: true,
        textButtonWidget: AppCommonWidgets.appBarTextButtonText(
            text: editProductDetailsBloc.productReferenceList.length == 1
                ? "Done"
                : AppStrings.next),
        onTapTextButton: () {
          editProductDetailsBloc.putEditApiCall();
        });
  }

  //endregion

  // region Body
  Widget body() {
    const double verticalSpace = 10;
    return SingleChildScrollView(
      child: Column(
        children: [
          addAndManageImage(),
          verticalSizedBox(verticalSpace),
          StreamBuilder<EditProductDetailsState>(
              stream: editProductDetailsBloc.editProductDetailCtrl.stream,
              initialData: EditProductDetailsState.Loading,
              builder: (context, snapshot) {
                if (snapshot.data == EditProductDetailsState.Success) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          //brandName(),
                          verticalSizedBox(verticalSpace),
                          basicDetailsOption(),
                          verticalSizedBox(verticalSpace),
                          inventoryOptions(),
                          verticalSizedBox(verticalSpace),
                          fulfillmentSetting(),
                          verticalSizedBox(verticalSpace),
                          returnSettings(),
                          verticalSizedBox(verticalSpace),
                          swadeshiLabels(),
                          verticalSizedBox(verticalSpace),
                          visibilityOption(),
                          verticalSizedBox(verticalSpace),
                          productPromotionsOption(),
                          verticalSizedBox(verticalSpace),
                          additionalInfo(),
                          AppCommonWidgets.bottomListSpace(context: context)
                        ],
                      ),
                    ),
                  );
                }
                if (snapshot.data == EditProductDetailsState.Loading) {
                  return Center(
                    child: AppCommonWidgets.appCircularProgress(),
                  );
                }
                return Center(
                  child: AppCommonWidgets.errorMessage(
                      error: AppStrings.commonErrorMessage),
                );
              }),
        ],
      ),
    );
  }

  // endregion

  //region Inventory Options
  Widget inventoryOptions() {
    return AppCommonWidgets.settingOption(
      optionText: AppStrings.inventoryAndPricing,
      onTap: () {
        editProductDetailsBloc.goToInventoryOptions();
      },
      isArrowVisible: true,
      subtitle: 'Add variants (if any), set MRP, selling price, and stock.',
      subtitleColor: AppColors.writingColor2,
      arrowColor: AppColors.appBlack,
      optionTextColor: AppColors.appBlack,
      isDoneVisible: (editProductDetailsBloc
                      .getOnlyProductResponse.singleProductData!.options !=
                  null &&
              editProductDetailsBloc.getOnlyProductResponse.singleProductData!
                  .options!.isNotEmpty) ||
          (editProductDetailsBloc
                      .getOnlyProductResponse.singleProductData!.variants !=
                  null &&
              editProductDetailsBloc.getOnlyProductResponse.singleProductData!
                  .variants!.isNotEmpty),
    );
  }
  //endregion

  //region Add Images
  Widget addImage() {
    return StreamBuilder<EditProductImageState>(
        stream: editProductDetailsBloc.editProductImageCtrl.stream,
        builder: (context, snapshot) {
          if (snapshot.data == EditProductImageState.Success) {
            return Align(
              alignment: Alignment.center,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  StreamBuilder<bool>(
                      stream: editProductDetailsBloc.imageCtrl.stream,
                      initialData: false,
                      builder: (context, snapshot) {
                        if (editProductDetailsBloc.productImageResponse.data !=
                            null) {
                          return InkWell(
                            onTap: () async {
                              await editProductDetailsBloc
                                  .openSelectEditImageDirect();
                              // Refresh the UI after returning from the select screen
                              if (mounted) {
                                setState(() {});
                              }
                            },
                            child: Builder(
                              builder: (context) {
                                try {
                                  final state = editProductDetailsBloc
                                      .getProductImageState(
                                    editProductDetailsBloc
                                        .productReferenceList[0],
                                  );

                                  if (state != null &&
                                      state.orderedStructure.isNotEmpty) {
                                    final first = state.orderedStructure[0];
                                    if (first['type'] == 'pending') {
                                      // For pending images, use Image.file
                                      final pendingPath =
                                          first['path'] as String?;
                                      if (pendingPath != null &&
                                          pendingPath.isNotEmpty) {
                                        // Remove file:// prefix if present
                                        final filePath =
                                            pendingPath.startsWith('file://')
                                                ? pendingPath.substring(7)
                                                : pendingPath;
                                        return Container(
                                          width: 75,
                                          height: 75,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(
                                                0.1957 * 75),
                                            color: Colors.grey[200],
                                          ),
                                          child: ClipRRect(
                                            borderRadius: BorderRadius.circular(
                                                0.1957 * 75),
                                            child: Image.file(
                                              File(filePath),
                                              fit: BoxFit.cover,
                                              width: 75,
                                              height: 75,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return Image.asset(
                                                  'assets/images/no_image.png',
                                                  fit: BoxFit.cover,
                                                );
                                              },
                                            ),
                                          ),
                                        );
                                      }
                                    } else if (first['type'] == 'existing') {
                                      // For existing images, use CustomImageContainer with network URL
                                      final imgList = state.existingImages
                                          .where((e) =>
                                              e.productimageid == first['id'])
                                          .toList();
                                      if (imgList.isNotEmpty) {
                                        return CustomImageContainer(
                                          width: 75,
                                          height: 75,
                                          imageUrl: imgList.first.productImage,
                                          imageType:
                                              CustomImageContainerType.product,
                                        );
                                      }
                                    }
                                  }

                                  // Fallback to the first image from productImageResponse if available
                                  if (editProductDetailsBloc
                                              .productImageResponse.data !=
                                          null &&
                                      editProductDetailsBloc
                                          .productImageResponse
                                          .data!
                                          .isNotEmpty) {
                                    return CustomImageContainer(
                                      width: 75,
                                      height: 75,
                                      imageUrl: editProductDetailsBloc
                                          .productImageResponse
                                          .data![0]
                                          .productImage,
                                      imageType:
                                          CustomImageContainerType.product,
                                    );
                                  }
                                } catch (e) {
                                  print('Error loading preview image: $e');
                                }

                                // Default empty container if no image is available
                                return Container(
                                  width: 75,
                                  height: 75,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    color: Colors.grey[200],
                                    border:
                                        Border.all(color: Colors.grey[300]!),
                                  ),
                                  child: Icon(Icons.image,
                                      color: Colors.grey[400]),
                                );
                              },
                            ),

                            // Container(
                            //   height:75,
                            //   width: 75,
                            //   decoration: const BoxDecoration(
                            //       color: AppColors.appWhite,
                            //       borderRadius: BorderRadius.all(Radius.circular(11))
                            //   ),
                            //   child: ClipRRect(
                            //       borderRadius: const BorderRadius.all(Radius.circular(11)),
                            //       child:   editProductDetailsBloc.productImageResponse.data!.isEmpty?SvgPicture.asset(AppImages.productPlaceHolder,fit: BoxFit.fill,):
                            //       editProductDetailsBloc.productImageResponse.data![0].productImage==null?
                            //
                            //       SvgPicture.asset(AppImages.productPlaceHolder,fit: BoxFit.fill,)
                            //           :extendedImage(editProductDetailsBloc.productImageResponse.data![0].productImage.toString(),
                            //         customPlaceHolder: AppImages.productPlaceHolder,
                            //         context,200,200,cache: true, )
                            //   ),
                            // ),
                          );
                        }
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(CommonMethods()
                              .getBorderRadius(
                                  height: 75,
                                  imageType: CustomImageContainerType.product)),
                          child: Container(
                            height: 75,
                            width: 75,
                            decoration: BoxDecoration(
                                color: AppColors.lightGray2,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(11))),
                          ),
                        );
                      }),
                  verticalSizedBox(24),
                  Consumer<AppConfigDataModel>(
                    builder: (BuildContext context, AppConfigDataModel value,
                        Widget? child) {
                      return CupertinoButton(
                          alignment: Alignment.center,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(9)),
                          color: AppColors.brandBlack,
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(AppImages.plus,
                                  color: AppColors.appWhite,
                                  height: 16,
                                  width: 16),
                              horizontalSizedBox(10),
                              Text(
                                "${AppStrings.addImages} (up to ${editProductDetailsBloc.getAvailableImageSlots(editProductDetailsBloc.productReferenceList[0], value.appConfig!.productImageLimit)})",
                                style: AppTextStyle.contentText0(
                                    textColor: AppColors.appWhite),
                              )
                            ],
                          ),
                          onPressed: () {
                            editProductDetailsBloc.openSelectEditImageDirect();
                          });
                    },
                  )
                ],
              ),
            );
          }
          if (snapshot.data == EditProductImageState.Loading) {
            return Center(
              child: AppCommonWidgets.appCircularProgress(),
            );
          }
          return SvgPicture.asset(
            AppImages.productPlaceHolder,
            height: 75,
            width: 75,
          );
        });
  }

  //endregion

  //region Add Images
  Widget addAndManageImage() {
    return StreamBuilder<EditProductImageState>(
      stream: editProductDetailsBloc.editProductImageCtrl.stream,
      builder: (context, snapshot) {
        if (snapshot.data == EditProductImageState.Success) {
          return Align(
            alignment: Alignment.center,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                StreamBuilder<bool>(
                  stream: editProductDetailsBloc.imageCtrl.stream,
                  initialData: false,
                  builder: (context, _) {
                    // Prepare state and ordered structure
                    final productRef =
                        editProductDetailsBloc.productReferenceList[0];
                    var state =
                        editProductDetailsBloc.getProductImageState(productRef);

                    // Initialize state if absent using current product images
                    if (state == null) {
                      final existing =
                          editProductDetailsBloc.productImageResponse.data ??
                              [];
                      final ordered = existing
                          .where((e) => e.productimageid != null)
                          .map<Map<String, dynamic>>((e) => {
                                'type': 'existing',
                                'id': e.productimageid,
                              })
                          .toList();
                      editProductDetailsBloc.updateProductImageState(
                        productRef,
                        existingImages: existing,
                        pendingImages: const [],
                        deletedImageIds: const [],
                        orderedStructure: ordered,
                        hasUnsavedChanges: false,
                      );
                      state = editProductDetailsBloc
                          .getProductImageState(productRef);
                    }

                    // Build display list from state.orderedStructure or fallback
                    List<Map<String, dynamic>> displayOrder = [];
                    if (state != null && state.orderedStructure.isNotEmpty) {
                      displayOrder = List<Map<String, dynamic>>.from(
                          state.orderedStructure);
                    } else {
                      final existing =
                          editProductDetailsBloc.productImageResponse.data ??
                              [];
                      displayOrder = existing
                          .where((e) => e.productimageid != null)
                          .map<Map<String, dynamic>>((e) => {
                                'type': 'existing',
                                'id': e.productimageid,
                              })
                          .toList();
                    }

                    // If nothing to show, render placeholder aspect ratio
                    if (displayOrder.isEmpty) {
                      return AspectRatio(
                        aspectRatio: 16 / 9,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(
                            CommonMethods().getBorderRadius(
                              height: 75,
                              imageType: CustomImageContainerType.product,
                            ),
                          ),
                          child: Container(
                            decoration: BoxDecoration(
                              color: AppColors.textFieldFill2,
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(10)),
                            ),
                            child: FittedBox(
                              fit: BoxFit.contain,
                              child: SvgPicture.asset(
                                AppImages.productPlaceHolder,
                                height: 45,
                                width: 45,
                              ),
                            ),
                          ),
                        ),
                      );
                    }
                    const double imageHeight = 200;
                    return InkWell(
                      onTap: () async {
                        await editProductDetailsBloc
                            .openSelectEditImageDirect();
                        if (mounted) setState(() {});
                      },
                      child: SizedBox(
                          height: imageHeight + 20,
                          child: Padding(
                            padding: const EdgeInsets.only(
                                left: 10, right: 10, top: 10, bottom: 10),
                            child: ReorderableListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: displayOrder.length,
                              itemBuilder: (context, index) {
                                final item = displayOrder[index];
                                Widget imageWidget;

                                if (item['type'] == 'pending') {
                                  final String? pendingPath =
                                      item['path'] as String?;
                                  final filePath = pendingPath != null &&
                                          pendingPath.startsWith('file://')
                                      ? pendingPath.substring(7)
                                      : pendingPath;
                                  imageWidget = Container(
                                    margin: const EdgeInsets.only(left: 8),
                                    decoration: BoxDecoration(
                                      color: AppColors.appWhite,
                                      borderRadius: BorderRadius.circular(11),
                                      boxShadow: AppColors.appShadow,
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(11),
                                      child: filePath != null
                                          ? PostAndProductImageWidgets(
                                              localOrNetworkImage: filePath,
                                              imageSize: imageHeight,
                                            )
                                          : Container(
                                              color: AppColors.lightGray2),
                                    ),
                                  );
                                } else {
                                  // existing
                                  final existingImages =
                                      state?.existingImages ??
                                          editProductDetailsBloc
                                              .productImageResponse.data ??
                                          [];
                                  final match = existingImages.firstWhere(
                                    (e) => e.productimageid == item['id'],
                                    orElse: () => existingImages.isNotEmpty
                                        ? existingImages.first
                                        : (throw ''),
                                  );
                                  // imageWidget = CustomImageContainer(
                                  //   width: 100,
                                  //   height: 200,
                                  //   imageUrl: match.productImage,
                                  //   imageType: CustomImageContainerType.product,
                                  // );
                                  imageWidget = Container(
                                    margin: const EdgeInsets.only(left: 8),
                                    decoration: BoxDecoration(
                                      color: AppColors.appWhite,
                                      borderRadius: BorderRadius.circular(11),
                                      boxShadow: AppColors.appShadow,
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(11),
                                      child: NetworkImageWithAspectRatio(
                                        url: match.productImage!
                                                .startsWith('/media/')
                                            ? "${AppConstants.baseUrl}${match.productImage}"
                                            : "${AppConstants.baseUrl}${AppConstants.baseMediaUrlWithslashes}${match.productImage}",
                                        height: imageHeight,
                                      ),
                                    ),
                                  );
                                }
                                return Container(
                                  key: ValueKey(
                                      '${item['type']}_${item['id'] ?? item['path']}_${index}'),
                                  // margin: const EdgeInsets.only(right: 8),
                                  child: Stack(
                                    children: [
                                      // Image container
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(
                                          CommonMethods().getBorderRadius(
                                            height: 75,
                                            imageType: CustomImageContainerType
                                                .product,
                                          ),
                                        ),
                                        child: imageWidget,
                                      ),
                                      // ClipRRect(
                                      //   borderRadius: BorderRadius.circular(
                                      //     CommonMethods().getBorderRadius(
                                      //       height: 75,
                                      //       imageType:
                                      //           CustomImageContainerType.product,
                                      //     ),
                                      //   ),
                                      //   child: Container(
                                      //     color: AppColors.lightGray2,
                                      //     child: imageWidget,
                                      //   ),
                                      // ),
                                      Positioned(
                                        top: 4,
                                        right: 4,
                                        child: InkWell(
                                          onTap: () {
                                            // Remove item: update state.deletedImageIds or pendingImages and orderedStructure
                                            final current =
                                                editProductDetailsBloc
                                                    .getProductImageState(
                                                        productRef);
                                            if (current == null) return;
                                            final newOrder =
                                                List<Map<String, dynamic>>.from(
                                                    displayOrder)
                                                  ..removeAt(index);

                                            List<int> newDeleted =
                                                List<int>.from(
                                                    current.deletedImageIds);
                                            if (item['type'] == 'existing' &&
                                                item['id'] != null) {
                                              newDeleted.add(item['id'] as int);
                                            }

                                            // Remove from pending if needed
                                            final newPending =
                                                List.of(current.pendingImages);
                                            if (item['type'] == 'pending' &&
                                                item['path'] != null) {
                                              newPending.removeWhere((p) =>
                                                  p.path == item['path']);
                                            }

                                            editProductDetailsBloc
                                                .updateProductImageState(
                                              productRef,
                                              deletedImageIds: newDeleted,
                                              pendingImages: newPending,
                                              orderedStructure: newOrder,
                                              hasUnsavedChanges: true,
                                            );
                                            setState(() {});
                                          },
                                          child: SvgPicture.asset(
                                              AppImages.removeCircle),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                              onReorder: (oldIndex, newIndex) {
                                setState(() {
                                  if (oldIndex < newIndex) newIndex -= 1;
                                  final moved = displayOrder.removeAt(oldIndex);
                                  displayOrder.insert(newIndex, moved);
                                  editProductDetailsBloc
                                      .updateProductImageState(
                                    productRef,
                                    orderedStructure: displayOrder,
                                    hasUnsavedChanges: true,
                                  );
                                });
                              },
                            ),
                          )),
                    );
                  },
                ),
                verticalSizedBox(10),
                Consumer<AppConfigDataModel>(
                  builder: (BuildContext context, AppConfigDataModel value,
                      Widget? child) {
                    return Align(
                      alignment: Alignment.center,
                      child: InkWell(
                          onTap: () async {
                            await editProductDetailsBloc
                                .openSelectEditImageDirect();
                            if (mounted) setState(() {});
                          },
                          child: Padding(
                            padding: const EdgeInsets.only(left: 10, right: 10),
                            child: Text(
                              editProductDetailsBloc.getAvailableImageSlots(
                                          editProductDetailsBloc
                                              .productReferenceList[0],
                                          value.appConfig!.productImageLimit) >
                                      0
                                  ? ' Product Images  •  Add upto ${value.appConfig!.productImageLimit} • Hold & Drag to reorder'
                                  : ' ${value.appConfig!.productImageLimit} images added',
                              style: AppTextStyle.smallTextRegular(
                                  textColor: AppColors.writingColor2),
                            ),
                          )),
                    );
                  },
                ),
              ],
            ),
          );
        }
        if (snapshot.data == EditProductImageState.Loading) {
          return Center(
            child: AppCommonWidgets.appCircularProgress(),
          );
        }
        return SvgPicture.asset(
          AppImages.productPlaceHolder,
          height: 75,
          width: 75,
        );
      },
    );
  }

  //endregion

// Add edit product Text fields
  Widget addEditProductTextFields() {
    return const AddEditProductFields();
  }
  //endregion

  //region Delivery Setting
  // Widget deliverySetting(){
  //   return EditProductCommonWidgets.greenButton(buttonName: AppStrings.setDeliverySettings, onPress: (){
  //     editProductDetailsBloc.goToDeliverSettingScreen();
  //
  //   });
  // }
  //endregion

  //region Return Policy
  // Widget returnPolicy(){
  //   return EditProductCommonWidgets.greenButton(buttonName: AppStrings.setReturnPolicy, onPress: (){
  //     editProductDetailsBloc.goToSellerReturnProductWarranty();
  //     //addProductBloc.goToReturnPolicy();
  //   });
  // }
//endregion

  //region Add labels
  Widget swadeshiLabels() {
    return AppCommonWidgets.settingOption(
      optionText: AppStrings.swadeshiLabels,
      onTap: () {
        editProductDetailsBloc.goToLabels(
            storeReference: editProductDetailsBloc
                .getOnlyProductResponse.singleProductData!.storeReference!);
      },
      subtitle: 'Select brand & product Swadeshi levels for visibility boost.',
      subtitleColor: AppColors.writingColor2,
      isArrowVisible: true,
      arrowColor: AppColors.appBlack,
      optionTextColor: AppColors.appBlack,
      isDoneVisible: editProductDetailsBloc
                  .getOnlyProductResponse.singleProductData!.swadeshiBrand !=
              null &&
          editProductDetailsBloc
                  .getOnlyProductResponse.singleProductData!.swadeshiMade !=
              null,
    );
  }
  //endregion

  //region Basic Details Option
  Widget basicDetailsOption() {
    return AppCommonWidgets.settingOption(
      optionText: 'Basic Details',
      onTap: () {
        goToBasicDetailsScreen();
      },
      subtitle: 'Brand, product name, category, and description.',
      subtitleColor: AppColors.writingColor3,
      isArrowVisible: true,
      arrowColor: AppColors.appBlack,
      optionTextColor: AppColors.appBlack,
      isDoneVisible: editProductDetailsBloc.isBasicDetailsFilled(),
    );
  }
  //endregion

  //region Navigate to Basic Details Screen
  void goToBasicDetailsScreen() async {
    var screen = const ProductTextFieldsScreen(
      isEditMode: true,
      section: ProductDetailsSection.basic,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    await Navigator.push(context, route);
    if (mounted) setState(() {});
  }
  //endregion

  //region Product Promotions Option
  Widget productPromotionsOption() {
    return AppCommonWidgets.settingOption(
      optionText: 'Promotions & Offers',
      onTap: () {
        goToProductPromotionsScreen();
      },
      subtitle: 'Set affiliate promotion or other offers.',
      subtitleColor: AppColors.writingColor2,
      isArrowVisible: true,
      arrowColor: AppColors.appBlack,
      optionTextColor: AppColors.appBlack,
      isDoneVisible: editProductDetailsBloc.isProductPromotionsFilled(),
      isOptional: true,
    );
  }
  //endregion

  void goToProductPromotionsScreen() async {
    var screen = const ProductTextFieldsScreen(
      isEditMode: true,
      section: ProductDetailsSection.promotions,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    await Navigator.push(context, route);
    if (mounted) setState(() {});
  }

  //region Visibility Option
  Widget visibilityOption() {
    return AppCommonWidgets.settingOption(
      optionText: 'Discovery & Tags',
      onTap: () {
        goToVisibilityScreen();
      },
      subtitle:
          'Product slug, code, hashtags, targeted gender — help buyers find it easily.',
      subtitleColor: AppColors.writingColor2,
      isArrowVisible: true,
      arrowColor: AppColors.appBlack,
      optionTextColor: AppColors.appBlack,
      isDoneVisible: editProductDetailsBloc.isVisibilityFilled(),
      isOptional: true,
    );
  }
  //endregion

  void goToVisibilityScreen() async {
    var screen = const ProductTextFieldsScreen(
      isEditMode: true,
      section: ProductDetailsSection.visibility,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    await Navigator.push(context, route);
    if (mounted) setState(() {});
  }

  //region More Details Option
  Widget additionalInfo() {
    return AppCommonWidgets.settingOption(
      optionText: 'Additional Info',
      onTap: () {
        goToMoreDetailsScreen();
      },
      subtitle:
          'All extra fields like promotion links and uncategorized details.',
      subtitleColor: AppColors.writingColor2,
      isArrowVisible: true,
      arrowColor: AppColors.appBlack,
      optionTextColor: AppColors.appBlack,
      isDoneVisible: editProductDetailsBloc.isMoreDetailsFilled(),
      isOptional: true,
    );
  }
  //endregion

  void goToMoreDetailsScreen() async {
    var screen = const ProductTextFieldsScreen(
      isEditMode: true,
      section: ProductDetailsSection.moreDetails,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    await Navigator.push(context, route);
    if (mounted) setState(() {});
  }

  //region Delivery Setting
  Widget fulfillmentSetting() {
    return AppCommonWidgets.settingOption(
      optionText: AppStrings.fulfillment,
      onTap: () {
        editProductDetailsBloc.goToDeliverSettingScreen();
      },
      subtitle:
          'Choose pickup location, shipping options, and delivery settings.',
      subtitleColor: AppColors.writingColor2,
      isArrowVisible: true,
      arrowColor: AppColors.appBlack,
      optionTextColor: AppColors.appBlack,
      isDoneVisible: true,
    );
  }
  //endregion

  //region Return Policy
  Widget returnSettings() {
    return AppCommonWidgets.settingOption(
      optionText: AppStrings.returnsAndRefunds,
      onTap: () {
        editProductDetailsBloc.goToSellerReturnProductWarranty();
      },
      subtitle: 'Set return eligibility, return window, and refund rules.',
      subtitleColor: AppColors.writingColor2,
      isArrowVisible: true,
      arrowColor: AppColors.appBlack,
      optionTextColor: AppColors.appBlack,
      isDoneVisible: true,
    );
  }
//endregion

  // void goToInventoryOptions({required String storeReference}) async {
  //   var screen = InventoryOptionsScreen(
  //     storeReference: storeReference,
  //     product: editProductDetailsBloc.getOnlyProductResponse.singleProductData!,
  //   );
  //   var route = MaterialPageRoute(builder: (context) => screen);
  //   Navigator.push(context, route).then((value) {
  //     if (value != null) {
  //       //If it has data
  //       if (value != null) {
  //         CommonMethods.toastMessage(
  //             AppStrings.inventoryOptionsUpdated, context,
  //             toastShowTimer: 3);
  //         //Refresh options
  //         optionsRefreshCtrl.sink.add(true);
  //       }
  //     } else {
  //       return;
  //     }
  //   });
  // }
}
