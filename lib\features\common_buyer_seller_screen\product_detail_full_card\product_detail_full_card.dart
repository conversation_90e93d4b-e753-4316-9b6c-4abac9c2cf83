import 'dart:io';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flip_card/flip_card.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:readmore/readmore.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_common_widgets.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buy_button/buy_button.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/product_availability/product_availability.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/product_label_dropdown/product_label_dropdown.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_image/product_detail_full_card_image.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/variant_selection_bottom_sheet.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_stories/product_detail_full_card_stories.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/post_and_product_appbar/post_and_product_appbar.dart';
import 'package:swadesic/features/widgets/post_widgets/comment_bottom_sheet_service.dart';
import 'package:swadesic/features/widgets/post_widgets/post_image.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/add_visited_reference/add_visited_references.dart';
import 'package:swadesic/services/app_analytics/app_analytics.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/onTapTag/onTapTag.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:swadesic/widgets/themed_image_icon.dart';

class ProductDetailFullCard extends StatefulWidget {
  final Product product;
  final bool isFromAddProduct;
  final isGoToLatestVersion;
  final bool isFullView;
  final String? customTitle;
  final bool? isCustomTitleVisible;

  const ProductDetailFullCard(
      {super.key,
      required this.product,
      this.isFromAddProduct = false,
      this.isGoToLatestVersion = false,
      this.isFullView = true,
      this.customTitle,
      this.isCustomTitleVisible = false});

  @override
  State<ProductDetailFullCard> createState() => _ProductDetailFullCardState();
}

class _ProductDetailFullCardState extends State<ProductDetailFullCard>
    with SingleTickerProviderStateMixin {
  //region Bloc
  late ProductDetailFullCardBloc productDetailFullCardBloc;
  bool _isLocalFileFuture = false;

  //endregion

  //region Like Animation
  late final AnimationController _likeAnimController;
  late final Animation<double> _likeScale;
  late final Animation<double> _likeOpacity;
  late final Animation<Offset> _likeSlide;
  bool _showLikeAnim = false;
  bool _showDoubleLikeAnim = false;
  Offset _tapPosition = Offset.zero;
  //endregion

  //region Init
  @override
  void initState() {
    productDetailFullCardBloc = ProductDetailFullCardBloc(
        context, widget.product, widget.isFromAddProduct);
    productDetailFullCardBloc.init();
    _likeAnimController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400), // Faster animation
    );
    final curve = CurvedAnimation(
      parent: _likeAnimController,
      curve: Curves.easeOutCubic, // More immediate curve
      reverseCurve: Curves.easeIn,
    );
    _likeScale = Tween<double>(begin: 0.5, end: 1.3).animate(curve);
    _likeOpacity = Tween<double>(begin: 0.8, end: 1.0)
        .chain(CurveTween(curve: Curves.easeOutCubic))
        .animate(_likeAnimController);
    _likeSlide =
        Tween<Offset>(begin: const Offset(0, 1), end: const Offset(0, -0.02))
            .chain(CurveTween(curve: Curves.easeOut))
            .animate(_likeAnimController);

    _likeAnimController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // Animation completed, start reverse
        _likeAnimController.reverse();
      } else if (status == AnimationStatus.dismissed) {
        // Animation fully reversed, hide it
        if (mounted) {
          setState(() {
            _showLikeAnim = false;
            _showDoubleLikeAnim = false;
          });
        }
      }
    });
    super.initState();
  }
  //endregion

  @override
  void dispose() {
    _likeAnimController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return SingleChildScrollView(
      controller: ProductDetailFullCardBloc.reviewsScrollController,
      child: VisibilityDetector(
        key: UniqueKey(),
        onVisibilityChanged: (visibilityInfo) {
          var visiblePercentage = visibilityInfo.visibleFraction * 100;
          if (visiblePercentage > 50) {
            //If from add product then do not add to visited post
            if (!widget.isFromAddProduct) {
              //Add to visited post
              AddVisitedReferences()
                  .addReferences(reference: widget.product.productReference!);
            }
          }
        },
        child: Consumer<ProductDataModel>(
          builder: (context, value, child) {
            Product product;
            if (widget.isFromAddProduct) {
              product = widget.product;
            } else {
              product = value.allProducts.firstWhere((element) =>
                  element.productReference == widget.product.productReference);
            }
            debugPrint(product.productReference);

            return GestureDetector(
                onTap: !widget.isFullView
                    ? () {
                        productDetailFullCardBloc.goToFullView(
                            product: product);
                      }
                    : null,
                onDoubleTapDown: (details) {
                  // Store tap position for animation
                  _tapPosition = details.localPosition;

                  // Reset animation controller and set appropriate state
                  _likeAnimController.reset();

                  // Determine animation type based on like status
                  if (product.likeStatus!) {
                    // Already liked - show double like animation only (don't toggle)
                    setState(() {
                      _showDoubleLikeAnim = true;
                      _showLikeAnim = false;
                    });
                  } else {
                    // Not liked - show single like animation and toggle like status
                    setState(() {
                      _showLikeAnim = true;
                      _showDoubleLikeAnim = false;
                    });
                    productDetailFullCardBloc.onTapHeart(product: product);
                  }

                  // Start animation immediately
                  _likeAnimController.forward();
                },
                child: Stack(
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      // padding: EdgeInsets.zero,
                      children: [
                        productAppBar(product: widget.product),
                        // Container(
                        //     color: AppColors.textFieldFill1,
                        //     width: CommonMethods.calculateWebWidth(context: context),
                        //     height: CommonMethods.calculateWebWidth(context: context),
                        //     child: product.prodImages!.isEmpty ? SvgPicture.asset(AppImages.productPlaceHolder) : productImage(product: product)),
                        //
                        ///Image
                        // widget.isFullView ? fullViewImage(product: product) : smallImageView(product: product),

                        productImageAndStory(product: product),

                        //Product and brand name
                        brandProduct(product: product),
                        // Ratings and orders info
                        productRatingAndSales(product: product),
                        //Price info
                        priceInfo(product: product),
                        //Product available
                        // Visibility(visible: widget.isFullView, child: Container(
                        //     margin: const EdgeInsets.only(top: 10),
                        //     child: ProductAvailability(product: product))),
                        //Sold and return
                        // soldAndReturns(product: product),
                        //Product labels
                        Container(
                            margin: const EdgeInsets.symmetric(vertical: 2),
                            child: ProductLabelDropdown(product: product)),
                        //Message
                        // message(product: product),
                        //Buy detail
                        buyDetail(),
                        const SizedBox(height: 5),
                        //Action
                        action(product: product),
                        //Counters
                        // counts(product: product),
                        //
                        const SizedBox(height: 25),
                      ],
                    ),
                    // Like animation overlay
                    if (_showLikeAnim)
                      Positioned(
                        left: _tapPosition.dx - 42, // Half of icon width (84/2)
                        top: _tapPosition.dy - 42, // Half of icon height (84/2)
                        child: IgnorePointer(
                          child: Visibility(
                            visible: _showLikeAnim,
                            child: SlideTransition(
                              position: _likeSlide,
                              child: FadeTransition(
                                opacity: _likeOpacity,
                                child: ScaleTransition(
                                  scale: _likeScale,
                                  child: SvgPicture.asset(
                                    AppImages.likeFilled,
                                    width: 84,
                                    height: 84,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),

                    // Double like animation overlay (for already liked products)
                    if (_showDoubleLikeAnim)
                      Positioned(
                        left: _tapPosition.dx - 42, // Half of icon width (84/2)
                        top: _tapPosition.dy - 42, // Half of icon height (84/2)
                        child: IgnorePointer(
                          child: Visibility(
                            visible: _showDoubleLikeAnim,
                            child: SlideTransition(
                              position: _likeSlide,
                              child: FadeTransition(
                                opacity: _likeOpacity,
                                child: ScaleTransition(
                                  scale: _likeScale,
                                  child: Stack(
                                    children: [
                                      // Main filled heart
                                      SvgPicture.asset(
                                        AppImages.likeFilled,
                                        width: 84,
                                        height: 84,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ));
          },
        ),
      ),
    );
  }

//endregion

  //region Product app bar
  Widget productAppBar({required Product product}) {
    return widget.isCustomTitleVisible! && widget.customTitle != null
        ? productRepostAppBar(
            productImage:
                product.prodImages == null || product.prodImages!.isEmpty
                    ? null
                    : product.prodImages!.first.productImage,
            productReference: product.productReference ?? '',
            storeHandle: product.storehandle ?? '',
            storeImage: product.storeIcon,
            storeReference: product.storeReference ?? '',
            level: product.level,
          )
        : productNormalAppBar(
            productImage:
                product.prodImages == null || product.prodImages!.isEmpty
                    ? null
                    : product.prodImages!.first.productImage,
            productReference: product.productReference ?? '',
            storeHandle: product.storehandle ?? '',
            storeImage: product.storeIcon,
            storeReference: product.storeReference ?? '',
            level: product.level,
            storeName: product.storeName,
            swadeshiOwned: product.swadeshiOwned,
          );
  }
  //endregion

  //region Product Rating and Sales
  Widget productRatingAndSales({required Product product}) {
    final hasRating = product.rating != null && product.rating! > 0;
    final hasSales = product.ordersCount != null && product.ordersCount! > 0;
    if (!hasRating && !hasSales) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (hasRating) {
          productDetailFullCardBloc.viewComment(
            productRef: product.productReference!,
          );
        }
      },
      onDoubleTapDown: (details) {
        // Store tap position for animation
        _tapPosition = details.localPosition;

        // Reset animation controller and set appropriate state
        _likeAnimController.reset();

        // Determine animation type based on like status
        if (product.likeStatus!) {
          // Already liked - show double like animation only (don't toggle)
          setState(() {
            _showDoubleLikeAnim = true;
            _showLikeAnim = false;
          });
        } else {
          // Not liked - show single like animation and toggle like status
          setState(() {
            _showLikeAnim = true;
            _showDoubleLikeAnim = false;
          });
          productDetailFullCardBloc.onTapHeart(product: product);
        }

        // Start animation immediately
        _likeAnimController.forward();
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
        child: Row(
          children: [
            // Rating section
            Visibility(
              visible: hasRating,
              child: InkWell(
                onTap: () {
                  productDetailFullCardBloc.viewComment(
                    productRef: product.productReference!,
                  );
                },
                child: Row(
                  children: [
                    // Rating bar with stars
                    RatingBarIndicator(
                      rating: product.rating ?? 0,
                      itemBuilder: (context, index) => SvgPicture.asset(
                        AppImages.star,
                        color: AppColors.yellow,
                      ),
                      itemCount: 5,
                      itemSize: 18.0,
                      direction: Axis.horizontal,
                      unratedColor: AppColors.textFieldFill1,
                    ),
                    const SizedBox(width: 8),
                    // Rating value
                    Text(
                      product.rating == null
                          ? '0'
                          : product.rating!.toStringAsFixed(1),
                      style: AppTextStyle.smallTextRegular(
                        textColor: AppColors.appBlack,
                      ),
                    ),
                    const SizedBox(width: 4),
                    // Reviews count
                    Text(
                      '• ${product.countOfRatings}${CommonMethods.singularPluralText(
                        item: product.countOfRatings ?? 0,
                        isValueReturn: false,
                        singular: AppStrings.reviewLowerCase,
                        plural: AppStrings.reviewsLowerCase,
                      )}',
                      style: AppTextStyle.smallTextRegular(
                        textColor: AppColors.appBlack,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Separator between rating and sales if both exist
            if (hasRating && hasSales) const SizedBox(width: 4), //
            // Sales section
            Visibility(
                visible: hasSales,
                child: Text(
                  hasRating && hasSales
                      ? '• ${product.ordersCount}${CommonMethods.singularPluralText(
                          item: product.ordersCount!,
                          isValueReturn: false,
                          singular: AppStrings.saleLowerCase,
                          plural: AppStrings.salesLowerCase,
                        )}'
                      : '${product.ordersCount}${CommonMethods.singularPluralText(
                          item: product.ordersCount!,
                          isValueReturn: false,
                          singular: AppStrings.saleLowerCase,
                          plural: AppStrings.salesLowerCase,
                        )}',
                  style: AppTextStyle.smallTextRegular(
                    textColor: AppColors.appBlack,
                  ),
                )),
          ],
        ),
      ),
    );
  }

  //endregion

  //region Product normal appbar
  Widget productNormalAppBar({
    required String productReference,
    required String storeReference,
    required String storeHandle,
    required String? storeImage,
    required String? productImage,
    String? storeName,
    String? swadeshiOwned,
    String? level,
  }) {
    return PostAndProductAppBar(
      onTapProfileImage: () {
        productDetailFullCardBloc.goToStore(storeReference: storeReference);
      },
      icon: storeImage,
      title: storeHandle,
      verifiedWidget: VerifiedBadge(
        width: 15,
        height: 15,
        subscriptionType: widget.product.subscriptionType,
      ),
      // Use safe fallbacks to prevent null crash in preview
      subTitle: storeName != null && storeName.isNotEmpty
          ? '${storeName.trim()} \u2022 ${CommonMethods.labelStatusToSentence(input: (swadeshiOwned ?? ''))}'
          : CommonMethods.labelStatusToSentence(input: (swadeshiOwned ?? '')),
      level: level,
      entityType: EntityType.STORE.name,
      onTapOptions: () {
        widget.isFromAddProduct
            ? null
            : productDetailFullCardBloc.onTapDrawer(
                productReference: productReference,
                productImage: productImage,
                storeReference: storeReference);
      },
    );
    // return GestureDetector(
    //   onTap: () {
    //     productDetailFullCardBloc.goToStore(storeReference: storeReference);
    //   },
    //   child: Container(
    //     alignment: Alignment.centerLeft,
    //     padding: const EdgeInsets.all(10),
    //     child: Row(
    //       children: [
    //         ///Un-comment
    //         CustomImageContainer(
    //           width: 30,
    //           height: 30,
    //           imageUrl: storeImage,
    //           imageType: CustomImageContainerType.store,
    //         ),
    //         const SizedBox(
    //           width: 10,
    //         ),
    //         Text(
    //           storeHandle.toLowerCase(),
    //           style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    //         ),
    //         const Expanded(
    //           child: SizedBox(
    //             width: 10,
    //           ),
    //         ),
    //         CupertinoButton(
    //             padding: EdgeInsets.zero,
    //             onPressed: () {
    //               //If from add product then return
    //               widget.isFromAddProduct
    //                   ? null
    //                   : productDetailFullCardBloc.onTapDrawer(
    //                       productReference: productReference, productImage: productImage, storeReference: storeReference);
    //             },
    //             child: SvgPicture.asset(AppImages.drawerIcon))
    //       ],
    //     ),
    //   ),
    // );
  }

  //endregion

  //region Product repost appbar
  Widget productRepostAppBar({
    required String productReference,
    required String storeReference,
    required String storeHandle,
    required String? storeImage,
    required String? productImage,
    String? level,
  }) {
    // Check if a subtitle (customTitle) is present
    bool hasSubtitle =
        widget.customTitle != null && widget.customTitle!.isNotEmpty;

    return PostAndProductAppBar(
      onTapProfileImage: () {
        productDetailFullCardBloc.goToStore(storeReference: storeReference);
      },
      icon: storeImage,
      title: "",
      level: level,
      customTitle: hasSubtitle
          // If subtitle is present, show date beside handle
          ? Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  storeHandle,
                  style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack)
                      .copyWith(height: 0),
                  overflow: TextOverflow.ellipsis,
                ),
                VerifiedBadge(
                  width: 15,
                  height: 15,
                  subscriptionType: widget.product.subscriptionType,
                ),
                const SizedBox(
                  width: 10,
                ),
                Text(
                  productDetailFullCardBloc.convertDateFormat(
                      inputDateTimeString: widget.product.createdDate!),
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyle.smallTextRegular(
                          textColor: AppColors.writingBlack0)
                      .copyWith(height: 0),
                )
              ],
            )
          // If no subtitle, only show handle (date will be shown below)
          : Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  storeHandle,
                  style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack)
                      .copyWith(height: 0),
                  overflow: TextOverflow.ellipsis,
                ),
                VerifiedBadge(
                  width: 15,
                  height: 15,
                  subscriptionType: widget.product.subscriptionType,
                ),
              ],
            ),
      // If no subtitle, show date as subtitle
      subTitle: !hasSubtitle
          ? productDetailFullCardBloc.convertDateFormat(
              inputDateTimeString: widget.product.createdDate!)
          : "",
      customSubTitle: hasSubtitle
          ? ReadMoreText(
              '${widget.customTitle}',
              trimMode: TrimMode.Line,
              trimLines: widget.isFullView ? 1000 : 3000,
              colorClickableText: Colors.pink,
              style: AppTextStyle.subTitle(textColor: AppColors.appBlack)
                  .copyWith(fontSize: 12, height: 0),
              lessStyle:
                  AppTextStyle.contentText0(textColor: AppColors.writingBlack1)
                      .copyWith(height: 0),
              moreStyle:
                  AppTextStyle.contentText0(textColor: AppColors.writingBlack1)
                      .copyWith(height: 0),
              trimLength: 100000,
              trimCollapsedText: "",
              trimExpandedText: "",
              textAlign: TextAlign.start,
              annotations: [
                Annotation(
                  regExp: RegExp(r'@([a-zA-Z0-9_]+)'),
                  spanBuilder: ({required String text, TextStyle? textStyle}) =>
                      TextSpan(
                    text: text.replaceFirst("@", ""),
                    style: textStyle
                        ?.copyWith(
                            color: AppColors.appBlack,
                            fontSize: 12,
                            fontFamily: AppConstants.rRegular,
                            fontWeight: FontWeight.w600)
                        .copyWith(height: 0),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        // Extract the username from the tapped text
                        final userName = text.substring(1);
                        OnTapTag(context, userName);
                      },
                  ),
                ),
              ],
            )
          : null,
      entityType: EntityType.STORE.name,
      onTapOptions: () {
        widget.isFromAddProduct
            ? null
            : productDetailFullCardBloc.onTapDrawer(
                productReference: productReference,
                productImage: productImage,
                storeReference: storeReference);
      },
    );
  }

  //endregion

  ///Modified
  //region Brand name And Product name
  Widget brandProduct({required Product product}) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, top: 6),
      child: SizedBox(
        width: double.infinity,
        child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            productDetailFullCardBloc.goToProductDetail(
                selectedProduct: product);
          },
          onDoubleTap: () {
            // Trigger like animation for product name/brand area
            _likeAnimController.reset();

            // Set tap position to center-top area for text tap animation
            _tapPosition = Offset(MediaQuery.of(context).size.width * 0.3,
                MediaQuery.of(context).size.height * 0.2);

            // Determine animation type based on current like status
            if (product.likeStatus!) {
              // Already liked - show double like animation (don't toggle)
              setState(() {
                _showDoubleLikeAnim = true;
                _showLikeAnim = false;
              });
            } else {
              // Not liked - show single like animation and toggle
              setState(() {
                _showLikeAnim = true;
                _showDoubleLikeAnim = false;
              });
            }

            _likeAnimController.forward();

            // Handle the like toggle
            productDetailFullCardBloc.onTapHeart(product: product);
          },
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                  child:
                      //Full view
                      widget.isFullView
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(product.brandName!,
                                    style: AppTextStyle.contentHeading0(
                                        textColor: AppColors.appBlack)),
                                Text(product.productName!,
                                    maxLines: 3,
                                    style: AppTextStyle.contentText0(
                                        textColor: AppColors.writingBlack0)),
                              ],
                            )
                          //Small view
                          : RichText(
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                      text: product.brandName!, // Bold text
                                      style: AppTextStyle.contentHeading0(
                                          textColor: AppColors.appBlack)),
                                  TextSpan(
                                      text: ' ${product.productName!}',
                                      style: AppTextStyle.contentText0(
                                          textColor: AppColors.appBlack)),
                                ],
                              ),
                            )),
              Visibility(
                visible: widget.isFullView,
                child: CupertinoButton(
                    padding: const EdgeInsets.only(left: 10, right: 6),
                    onPressed: () {
                      productDetailFullCardBloc.goToProductDetail(
                          selectedProduct: product);
                    },
                    child: SvgPicture.asset(
                      AppImages.arrow3,
                      height: 25,
                      color: AppColors.appBlack,
                    )),
              )
            ],
          ),
        ),
      ),
    );
  }

//endregion
  /// Price info
// region Price info
  Widget priceInfo({required Product product}) {
    final sellingPrice = productDetailFullCardBloc.getDisplaySellingPrice();
    final mrpPrice = productDetailFullCardBloc.getDisplayMrpPrice();
    final discountPercentage =
        productDetailFullCardBloc.getDisplayDiscountPercentage();
    final stock = productDetailFullCardBloc.getDisplayStock();

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (productDetailFullCardBloc.hasVariants()) {
          _showVariantSelectionBottomSheet(product);
        }
        productDetailFullCardBloc.goToProductDetail(selectedProduct: product);
      },
      child: Container(
        margin: const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 8),
        decoration: productDetailFullCardBloc.hasVariants()
            ? BoxDecoration(
                color: AppColors.textFieldFill2.withOpacity(0.6),
                borderRadius: BorderRadius.circular(8),
              )
            : null,
        padding: productDetailFullCardBloc.hasVariants()
            ? const EdgeInsets.symmetric(horizontal: 8, vertical: 8)
            : null,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Show variant info only if we have selectable variants
            if (productDetailFullCardBloc.hasVariants())
              Container(
                width: double.infinity,
                child: Column(
                  children: [
                    Row(
                      children: [
                        Text(
                          "Selected: ",
                          style: AppTextStyle.smallText(
                            textColor: AppColors.appBlack,
                          ),
                        ),
                        Expanded(
                          child: _buildVariantDisplayText(),
                        ),
                        const SizedBox(width: 4),
                        SvgPicture.asset(
                          AppImages.arrow,
                          height: 20,
                          width: 28,
                          color: AppColors.appBlack,
                        ),
                      ],
                    ),
                    // const SizedBox(height: 8)
                  ],
                ),
              ),
            _buildPriceAndStockRow(),
          ],
        ),
      ),
    );
  }
// endregion

  Widget _buildPriceAndStockRow() {
    final sellingPrice = productDetailFullCardBloc.getDisplaySellingPrice();
    final mrpPrice = productDetailFullCardBloc.getDisplayMrpPrice();
    final discountPercentage =
        productDetailFullCardBloc.getDisplayDiscountPercentage();
    final stock = productDetailFullCardBloc.getDisplayStock();

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 0, vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.baseline,
        textBaseline: TextBaseline.alphabetic,
        children: [
          // Selling price
          Text(
            "₹$sellingPrice",
            style: AppTextStyle.pageHeading(
              textColor: AppColors.appBlack,
            ).copyWith(height: 0),
          ),
          const SizedBox(width: 8),

          // MRP with strikethrough
          if (sellingPrice != mrpPrice)
            Text(
              "₹$mrpPrice",
              style: AppTextStyle.access0(
                textColor: AppColors.writingBlack1,
                isLineThrough: true,
              ).copyWith(height: 0),
            ),

          // Discount percentage
          if (sellingPrice != mrpPrice && discountPercentage > 0) ...[
            const SizedBox(width: 8),
            Text(
              "${discountPercentage.toInt()}% OFF",
              style: AppTextStyle.access0(
                textColor: AppColors.brandGreen,
              ).copyWith(height: 0),
            ),
          ],

          const Spacer(),

          // Stock information
          // Text(
          //   stock == 0 ? "Out of stock" : "Only $stock available",
          //   style: AppTextStyle.smallText(
          //     textColor: stock == 0 ? AppColors.red : AppColors.orange,
          //   ),
          // ),
          Text(
            stock > 10
                ? "" //$stock units available
                : stock > 0
                    ? "Only $stock left"
                    : "Out of stock",
            style: AppTextStyle.smallText(
              textColor: stock > 10
                  ? AppColors.appBlack
                  : stock > 0
                      ? AppColors.orange
                      : AppColors.red,
            ),
          ),
        ],
      ),
    );
  }

  ///New
//region Sold and returns
//   Widget soldAndReturns({required Product product}) {
//     return Visibility(
//       visible: product.ordersCount != 0 || product.returnCount! != 0,
//       child: Padding(
//         padding: const EdgeInsets.only(left: 16, right: 16, bottom: 7),
//         child: Align(
//           alignment: Alignment.centerLeft,
//           child: Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               Visibility(
//                   visible: product.ordersCount! > 0,
//                   child: Text(CommonMethods.singularPluralText(item: product.ordersCount!, singular: "purchase", plural: "purchases"),
//                       style: AppTextStyle.smallText(textColor: AppColors.brandGreen))),
//               const SizedBox(
//                 width: 5,
//               ),
//               Visibility(
//                   visible: product.returnCount! > 0,
//                   child: Text(CommonMethods.singularPluralText(item: product.returnCount!, singular: "return", plural: "returns"),
//                       style: AppTextStyle.smallText(textColor: AppColors.orange))),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
//endregion

  ///Remove
  //region Product images
  Widget productImage({required Product product}) {
    int onScreenImageIndex = 0;
    return Stack(
      alignment: Alignment.center,
      children: [
        GestureDetector(
          onDoubleTap: () {
            // buyerViewProductBloc.onTapHeart(product: product,isDoubleTapped: true);
          },
          child: PageView.builder(
              controller: productDetailFullCardBloc.pageController,
              allowImplicitScrolling: true,
              onPageChanged: (index) {
                onScreenImageIndex = index;
                //print(index);
                productDetailFullCardBloc.sliderCtrl.sink.add(index);
              },
              itemCount: product.prodImages!.length,

              ///controller: buyerViewProductBloc.imageSliderPageCtrl,
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap: !widget.isFromAddProduct
                      ? () {
                          //print(onScreenImageIndex);
                          productDetailFullCardBloc.goToBuyerProductImageScreen(
                              productImage: product.prodImages!,
                              imageIndex: onScreenImageIndex);
                        }
                      : null,
                  child: CommonMethods.networkOrLocal(
                          product.prodImages![index].getDisplayImageUrl() ??
                              product.prodImages![index].productImage!)
                      ? extendedImage(
                          product.prodImages![index].getDisplayImageUrl() ??
                              product.prodImages![index].productImage!,
                          fit: BoxFit.cover,
                          context,
                          500,
                          500,
                          cache: true,
                          customPlaceHolder: AppImages.productPlaceHolder)
                      : Image.file(
                          File(product.prodImages![index].productImage!),
                          fit: BoxFit.cover, // Adjust the fit as needed
                        ),
                );
              }),
        ),
        Positioned(
          bottom: 0,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: SizedBox(
              height: 6,
              child: StreamBuilder<int>(
                  stream: productDetailFullCardBloc.sliderCtrl.stream,
                  initialData: 0,
                  builder: (context, snapshot) {
                    return ListView.builder(
                        itemCount: product.prodImages!.length,
                        scrollDirection: Axis.horizontal,
                        shrinkWrap: true,
                        itemBuilder: (Context, Index) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 5),
                            child: SvgPicture.asset(
                              AppImages.dot,
                              height: 5.29,
                              width: 5.29,
                              color: snapshot.data == Index
                                  ? AppColors.darkGray
                                  : AppColors.darkStroke,
                            ),
                          );
                        });
                  }),
            ),
          ),
        ),
        const Positioned(
          top: 20,
          left: 15,
          right: 15,
          child: SizedBox(
            height: 35,
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // productRatings(productIndex),
                // save(productIndex),
              ],
            ),
          ),
        )
      ],
    );
  }

//endregion

//region Buy Now and View detail
  Widget buyDetail() {
    // Retrieve the data from the StoreInfoModel

    return Container(
      margin: const EdgeInsets.only(top: 5),
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Visibility(
              visible: !widget.isFromAddProduct &&
                  widget.product.storeReference ==
                      AppConstants.appData.storeReference &&
                  !widget.product.isDeleted!,
              child: Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: CupertinoButton(
                        onPressed: () {
                          productDetailFullCardBloc.onTapShare(
                            imageUrl: widget.product.prodImages!.isEmpty
                                ? null
                                : widget.product.prodImages!.first.productImage,
                            productReference: widget.product.productReference!,
                          );
                        },
                        padding: EdgeInsets.zero,
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(
                              vertical: 10, horizontal: 10),
                          decoration: BoxDecoration(
                            color: AppColors.appBlack,
                            borderRadius: BorderRadius.circular(13),
                          ),
                          child: Center(
                            child: Text(
                              'Share Product',
                              style: AppTextStyle.access0(
                                  textColor: AppColors.appWhite),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 10),
                  ],
                ),
              ),
            ),
            Expanded(
              child: BuyButton(
                product: widget.product,
                isFromAddProduct: widget.isFromAddProduct,
                isGoToLatestVersion: widget.isGoToLatestVersion,
                productDetailFullCardBloc: productDetailFullCardBloc,
              ),
            ),
            // //Go to latest version
            // widget.isGoToLatestVersion
            //     ? Expanded(
            //         child: Container(
            //           // child: ProductCommonWidgets.buyAddAndGoToButton(
            //           //     product: product,
            //           //     buttonName: AppStrings.goToLatestVersion,
            //           //     onTap: () {
            //           //       productDetailFullCardBloc.goToSingleProductScreen(productReference: product.productReference!);
            //           //     }),
            //         ),
            //       )
            //     :
            //     //Buy button
            //     Expanded(
            //         child: Container(
            //             decoration: const BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(10))),
            //             child: BuyButton(
            //               product: product,
            //               isFromAddProduct: widget.isFromAddProduct,
            //             )),
            //       ),

            //Edit
            Visibility(
                visible: !widget.isFromAddProduct &&
                    widget.product.storeReference ==
                        AppConstants.appData.storeReference &&
                    !widget.product.isDeleted!,
                child: InkWell(
                  // padding: EdgeInsets.zero,
                  onTap: () {
                    productDetailFullCardBloc.onTapEditDetails(
                        productReference: widget.product.productReference!,
                        storeId: widget.product.storeid!);
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(
                      left: 10,
                      top: 10,
                      bottom: 10,
                    ),
                    child: Icon(
                      Icons.edit_note,
                      color: AppColors.appBlack,
                    ),
                  ),
                ))
          ],
        ),
      ),
    );
  }

//endregion

  //region Message
  Widget message({required Product product}) {
    return Visibility(
      visible: !product.isBuyEnable!,
      child: Row(
        children: [
          Container(
            margin: const EdgeInsets.only(bottom: 7, left: 18, right: 18),
            padding: const EdgeInsets.symmetric(horizontal: 5),
            alignment: Alignment.centerLeft,
            decoration: BoxDecoration(
                color: AppColors.textFieldFill2,
                borderRadius: BorderRadius.circular(5)),
            child: Text(
              product.productStatusMessage!,
              maxLines: 1,
              textAlign: TextAlign.left,
              overflow: TextOverflow.ellipsis,
              style: AppTextStyle.smallText(textColor: AppColors.orange),
            ),
          ),
        ],
      ),
    );
  }

  //endregion

  //region Counts
  Widget counts({required Product product}) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 5),
      child: Row(
        children: [
          Visibility(
            visible: product.likeCount != 0,
            child: InkWell(
              onTap: () {
                productDetailFullCardBloc.goToLikedUsedOrStoreScreen(
                    reference: product.productReference!);
              },
              child: Container(
                  margin: const EdgeInsets.only(right: 5),
                  child: Text(
                    "${product.likeCount} ${product.likeCount == 1 ? "like" : "likes"}",
                    style: AppTextStyle.smallText(
                        textColor: AppColors.writingBlack0),
                  )),
            ),
          ),
          Visibility(
            visible: product.commentCount != 0,
            child: Container(
                margin: const EdgeInsets.only(right: 5),
                child: InkWell(
                  onTap: () {
                    productDetailFullCardBloc.viewComment(
                        productRef: product.productReference!);
                  },
                  child: Text(
                      "${product.commentCount} ${product.commentCount == 1 ? "comment" : "comments"}",
                      style: AppTextStyle.smallText(
                          textColor: AppColors.writingBlack0)),
                )),
          ),
          const Expanded(child: SizedBox()),
          // Visibility(
          //   visible: product.analyticsViewCount != null &&
          //       product.analyticsViewCount! > 0,
          //   child: Container(
          //       margin: const EdgeInsets.only(right: 0),
          //       child: Text(
          //           "${product.analyticsViewCount} ${product.analyticsViewCount == 1 ? "view" : "views"}",
          //           style: AppTextStyle.smallText(
          //               textColor: AppColors.writingBlack0))),
          // ),
        ],
      ),
    );
  }

  //endregion

  // //region Action
  // Widget action2({required Product product}) {
  //   return IgnorePointer(
  //     ignoring: widget.isFromAddProduct,
  //     child: Container(
  //       margin: const EdgeInsets.only(top: 10),
  //       padding: const EdgeInsets.symmetric(horizontal: 16),
  //       child: Row(
  //         children: [
  //           // Like
  //           Container(
  //             margin: const EdgeInsets.only(right: 10),
  //             height: 26,
  //             width: 26,
  //             child: CupertinoButton(
  //               padding: EdgeInsets.zero,
  //               onPressed: () {
  //                 productDetailFullCardBloc.onTapHeart(product: product);
  //               },
  //               child: SvgPicture.asset(
  //                 fit: BoxFit.fill,
  //                 product.likeStatus!
  //                     ? AppImages.postLike
  //                     : AppImages.postDisLike,
  //                 color:
  //                     product.likeStatus! ? AppColors.red : AppColors.appBlack,
  //               ),
  //             ),
  //           ),

  //           // Comment
  //           Container(
  //             margin: const EdgeInsets.only(right: 10),
  //             height: 26,
  //             width: 26,
  //             child: CupertinoButton(
  //               padding: EdgeInsets.zero,
  //               onPressed: () {
  //                 CommentBottomSheetService.showProductCommentBottomSheet(
  //                   context: context,
  //                   productReference: product.productReference!,
  //                   onCommentAdded: () {
  //                     // Comment added successfully
  //                     // The comment will be visible when user navigates to comment view
  //                   },
  //                 );
  //               },
  //               child: SvgPicture.asset(AppImages.postComment,
  //                   color: AppColors.appBlack),
  //             ),
  //           ),

  //           // Repost
  //           InkWell(
  //             onTap: () {
  //               productDetailFullCardBloc.rePost(product: widget.product);
  //             },
  //             child: SizedBox(
  //               height: 26,
  //               width: 26,
  //               child: Stack(
  //                 alignment: Alignment.center,
  //                 children: [
  //                   SvgPicture.asset(
  //                     AppImages.repost,
  //                     color: widget.product.contentCategory ==
  //                             EntityType.REPOST.name
  //                         ? AppColors.brandGreen
  //                         : AppColors.appBlack,
  //                     width: 24,
  //                     height: 24,
  //                   ),
  //                   if (widget.product.contentCategory ==
  //                       EntityType.REPOST.name)
  //                     SvgPicture.asset(
  //                       AppImages.repost,
  //                       color: AppColors.brandGreen,
  //                       width: 22, // Slightly smaller to create a bolder effect
  //                       height: 22,
  //                     ),
  //                 ],
  //               ),
  //             ),
  //           ),

  //           const Expanded(child: SizedBox()),

  //           // Share
  //           SizedBox(
  //             height: 26,
  //             width: 26,
  //             child: CupertinoButton(
  //                 padding: EdgeInsets.zero,
  //                 onPressed: !widget.isFromAddProduct
  //                     ? () {
  //                         productDetailFullCardBloc.onTapShare(
  //                             imageUrl: product.prodImages!.isEmpty
  //                                 ? null
  //                                 : product.prodImages!.first.productImage,
  //                             productReference: product.productReference!);
  //                       }
  //                     : null,
  //                 child: SvgPicture.asset(
  //                   AppImages.sharePost,
  //                   color: AppColors.appBlack,
  //                 )),
  //           ),
  //           const SizedBox(width: 10),
  //           // Save
  //           InkWell(
  //             onTap: () {
  //               productDetailFullCardBloc.save(product: widget.product);
  //             },
  //             child: widget.product.saveStatus!
  //                 ? Container(
  //                     padding: const EdgeInsets.only(
  //                         left: 4, right: 4, top: 2, bottom: 4),
  //                     height: 26,
  //                     width: 26,
  //                     child: SvgPicture.asset(AppImages.savePostActive,
  //                         color: AppColors.appBlack))
  //                 : SizedBox(
  //                     height: 26,
  //                     width: 26,
  //                     child: SvgPicture.asset(AppImages.savePost,
  //                         color: AppColors.appBlack)),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }
  // //endregion

  //region Action
  Widget action({required Product product}) {
    return IgnorePointer(
      ignoring: widget.isFromAddProduct,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
        child: Row(
          children: [
            // Like
            CupertinoButton(
              padding: EdgeInsets.zero,
              minSize: 0,
              onPressed: () {
                // Trigger like animation first
                _likeAnimController.reset();

                // Set tap position to center of screen for like button animation
                _tapPosition = MediaQuery.of(context).size.center(Offset.zero);

                // Determine animation type based on current like status
                if (product.likeStatus!) {
                  // Already liked - show double like animation (don't toggle)
                  // setState(() {
                  //   _showDoubleLikeAnim = true;
                  //   _showLikeAnim = false;
                  // });
                } else {
                  // Not liked - show single like animation and toggle
                  setState(() {
                    _showLikeAnim = true;
                    _showDoubleLikeAnim = false;
                  });
                }

                _likeAnimController.forward();

                // Then handle the like toggle
                productDetailFullCardBloc.onTapHeart(product: product);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
                margin: const EdgeInsets.only(right: 10),
                decoration: BoxDecoration(
                  color: AppColors.textFieldFill2.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Row(
                  children: [
                    product.likeStatus!
                        ? SvgPicture.asset(
                            AppImages.likeFilled,
                            width: 20,
                            height: 20,
                          )
                        : ThemedImageIcon.asset(
                            AppImages.likeOutlined,
                            width: 20,
                            height: 20,
                          ),
                    if (product.likeCount != null &&
                        product.likeCount! > 0) ...[
                      const SizedBox(width: 5),
                      Padding(
                        padding: const EdgeInsets.only(right: 4),
                        child: Text(
                          product.likeCount.toString(),
                          style: AppTextStyle.smallText(
                              textColor: AppColors.appBlack),
                        ),
                      )
                    ],
                  ],
                ),
              ),
            ),

            // Comment
            GestureDetector(
              onLongPress: () {
                productDetailFullCardBloc.viewComment(
                  productRef: product.productReference!,
                );
              },
              child: CupertinoButton(
                padding: EdgeInsets.zero,
                minSize: 0,
                onPressed: () {
                  if (CommonMethods().isStaticUser()) {
                    AppConstants.isSignInScreenOpenedFromProfileTab = true;
                    CommonMethods().goToSignUpFlow();
                    return;
                  }
                  CommentBottomSheetService.showProductCommentBottomSheet(
                    context: context,
                    productReference: product.productReference!,
                    onCommentAdded: () {
                      // Comment added successfully
                      // The comment will be visible when user navigates to comment view
                    },
                  );
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
                  margin: const EdgeInsets.only(right: 10),
                  decoration: BoxDecoration(
                    color: AppColors.textFieldFill2.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Row(
                    children: [
                      ThemedImageIcon.asset(
                        AppImages.commentOutlined,
                        width: 20,
                        height: 20,
                      ),
                      if (product.commentCount != null &&
                          product.commentCount! > 0) ...[
                        const SizedBox(width: 5),
                        Padding(
                          padding: const EdgeInsets.only(right: 4),
                          child: Text(
                            product.commentCount.toString(),
                            style: AppTextStyle.smallText(
                                textColor: AppColors.appBlack),
                          ),
                        )
                      ],
                    ],
                  ),
                ),
              ),
            ),

            // Repost
            CupertinoButton(
              padding: EdgeInsets.zero,
              minSize: 0,
              onPressed: () {
                productDetailFullCardBloc.rePost(product: widget.product);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
                margin: const EdgeInsets.only(right: 10),
                decoration: BoxDecoration(
                  color: AppColors.textFieldFill2.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Row(
                  children: [
                    // Stack(
                    //   alignment: Alignment.center,
                    //   children: [
                    //     SvgPicture.asset(
                    //       AppImages.repost,
                    //       width: 20,
                    //       height: 20,
                    //       color:
                    //           product.contentCategory == EntityType.REPOST.name
                    //               ? AppColors.brandGreen
                    //               : AppColors.appBlack,
                    //     ),
                    //     if (product.contentCategory == EntityType.REPOST.name)
                    //       SvgPicture.asset(
                    //         AppImages.repost,
                    //         color: AppColors.brandGreen,
                    //         width: 18,
                    //         height: 18,
                    //       ),
                    //   ],
                    // ),
                    ThemedImageIcon.asset(
                      widget.product.contentCategory == EntityType.REPOST.name
                          ? AppImages.repostFilled
                          : AppImages.repostOutlined,
                      width: 20,
                      height: 20,
                      // color: AppColors.appBlack,
                    ),
                    // if (widget.product.commentCount != 0) ...[
                    //   const SizedBox(
                    //     width: 5,
                    //   ),
                    //   Padding(
                    //     padding: const EdgeInsets.only(right: 4),
                    //     child: Text(
                    //       widget.product.commentCount.toString(),
                    //       style: AppTextStyle.smallText(
                    //           textColor: AppColors.appBlack),
                    //     ),
                    //   ),
                    // ],
                  ],
                ),
              ),
            ),

            // Share
            CupertinoButton(
              padding: EdgeInsets.zero,
              minSize: 0,
              onPressed: !widget.isFromAddProduct
                  ? () {
                      productDetailFullCardBloc.onTapShare(
                        imageUrl: product.prodImages?.isNotEmpty == true
                            ? product.prodImages!.first.productImage
                            : null,
                        productReference: product.productReference!,
                      );
                    }
                  : null,
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: AppColors.textFieldFill2.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: SvgPicture.asset(
                  AppImages.sendOutlined,
                  width: 20,
                  height: 20,
                  color: AppColors.appBlack,
                ),
              ),
            ),

            // Play video
            // TODO: In future, wrap this with conditional visibility based on video availability
            // Example: if (widget.product.videoData != null && widget.product.videoData!.isNotEmpty)
            CupertinoButton(
              padding: EdgeInsets.zero,
              minSize: 0,
              onPressed: () {
                productDetailFullCardBloc.playVideo(product: widget.product);
              },
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: AppColors.textFieldFill2.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Icon(
                  Icons.play_arrow,
                  size: 20,
                  color: AppColors.appBlack,
                ),
              ),
            ),

            const Expanded(child: SizedBox()),

            // Save
            CupertinoButton(
              padding: EdgeInsets.zero,
              minSize: 0,
              onPressed: () {
                productDetailFullCardBloc.save(product: widget.product);
              },
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: AppColors.textFieldFill2.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: SvgPicture.asset(
                  product.saveStatus!
                      ? AppImages.saveFilled
                      : AppImages.saveOutlined,
                  width: 20,
                  height: 20,
                  color: AppColors.appBlack,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  //endregion

//region Product image and story
  Widget productImageAndStory({required Product product}) {
    //If from add product
    if (widget.isFromAddProduct) {
      return ProductDetailFullCardImage(
          product: product,
          productDetailFullCardBloc: productDetailFullCardBloc,
          isFromAddProduct: widget.isFromAddProduct,
          isFullView: widget.isFullView);
    }

    //Is small view
    if (!widget.isFullView) {
      return ProductDetailFullCardImage(
          product: product,
          productDetailFullCardBloc: productDetailFullCardBloc,
          isFromAddProduct: widget.isFromAddProduct,
          isFullView: widget.isFullView);
    }

    ///Todo if below commented code you are uncommenting then remove the below return statement .
    return ProductDetailFullCardImage(
        product: product,
        productDetailFullCardBloc: productDetailFullCardBloc,
        isFromAddProduct: widget.isFromAddProduct,
        isFullView: widget.isFullView);

    ///Todo Un-comment this if needed
    //If product has no tagged stories
    // else if (product.taggedStories!.isEmpty){
    //   return ProductDetailFullCardImage(product:product,productDetailFullCardBloc: productDetailFullCardBloc,isFromAddProduct: widget.isFromAddProduct,isFullView: widget.isFullView);
    // }

    //Else show product image and story option
    // return Stack(
    //   children: [
    //     ///Note un-commnet this if needed
    //     FlipCard(
    //       controller: productDetailFullCardBloc.flipController,
    //       fill: Fill.fillBack, // Fill the back side of the card to make in the same size as the front.
    //       direction: FlipDirection.VERTICAL, // default
    //       side: CardSide.FRONT, // The side to initially display.
    //       front: ProductDetailFullCardImage(product:product,productDetailFullCardBloc: productDetailFullCardBloc,isFromAddProduct: widget.isFromAddProduct,isFullView: widget.isFullView),
    //       back: ProductDetailFullCardStories(product:product,productDetailFullCardBloc: productDetailFullCardBloc,isFromAddProduct: widget.isFromAddProduct,isFullView: widget.isFullView),
    //     ),
    //     StreamBuilder<bool>(
    //       stream: productDetailFullCardBloc.switchProductImageAndStoryIconCtrl.stream,
    //       initialData: true,
    //       builder: (context, snapshot) {
    //         return Positioned(
    //             left: 30,
    //             top: 20,
    //             child: InkWell(
    //                 onTap: (){
    //                   productDetailFullCardBloc.switchImageAndStory(value: snapshot.data!);
    //                 },
    //                 child: Image.asset(
    //                     snapshot.data! ? AppImages.productImageSwitch :
    //                     AppImages.productStorySwitch,height: 40)));
    //       }
    //     )
    //   ],
    // );
  }
//endregion

  //region Variant Selection Methods
  Widget _buildVariantDisplayText() {
    if (productDetailFullCardBloc.selectedVariant != null) {
      final variant = productDetailFullCardBloc.selectedVariant!;
      if (variant.combinations.isEmpty) {
        return Text(
          "Standard",
          style: AppTextStyle.smallTextRegular(
            textColor: AppColors.appBlack,
          ),
        );
      }

      return Wrap(
        spacing: 10,
        runSpacing: 4,
        children: variant.combinations.entries.map((entry) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "${entry.key}: ",
                style: AppTextStyle.smallTextRegular(
                  textColor: AppColors.writingBlack1,
                ),
              ),
              Text(
                entry.value,
                style: AppTextStyle.smallText(
                  textColor: AppColors.appBlack,
                ),
              ),
            ],
          );
        }).toList(),
      );
    }
    return Text(
      "Standard",
      style: AppTextStyle.smallTextRegular(
        textColor: AppColors.appBlack,
      ),
    );
  }

  void _showVariantSelectionBottomSheet(Product product) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => VariantSelectionBottomSheet(
        product: product,
        productDetailFullCardBloc: productDetailFullCardBloc,
        onVariantSelected: (variant) {
          setState(() {
            productDetailFullCardBloc.selectVariant(variant);
          });
        },
      ),
    );
  }
  //endregion
}
