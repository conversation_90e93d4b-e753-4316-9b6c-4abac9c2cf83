import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/add_edit_product_fields/add_edit_product_fields.dart';
import 'package:swadesic/features/seller/add_product/add_product_common_widgets.dart';
import 'package:swadesic/features/seller/product_text_fields/product_text_fields_screen.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_dashboard_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/post_widgets/post_image.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'add_product_bloc.dart';
import 'package:reorderables/reorderables.dart';
import 'package:swadesic/util/auto_hide_navigation_service.dart';

// region Add Product Screen
class AddProductScreen extends StatefulWidget {
  final int storeId;
  final String storeReference;
  const AddProductScreen(
      {Key? key, required this.storeId, required this.storeReference})
      : super(key: key);

  @override
  _AddProductScreenState createState() => _AddProductScreenState();
}
// endregion

class _AddProductScreenState extends State<AddProductScreen>
    with AutoHideNavigationMixin<AddProductScreen> {
  // region Bloc
  late AddProductBloc addProductBloc;

  // endregion

  // region Init
  @override
  void initState() {
    addProductBloc =
        AddProductBloc(context, widget.storeId, widget.storeReference);
    addProductBloc.init();
    // Force-hide bottom navigation as soon as this screen opens
    enableAutoHideNavigation();
    forceHideNavigationBars();
    super.initState();
  }
  // endregion

  //region Dispose
  @override
  void dispose() {
    // Re-enable/show navigation when leaving this screen
    disableAutoHideNavigation();
    addProductBloc.dispose();
    super.dispose();
  }
  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Clear images for both platforms
        AppConstants.multipleSelectedImage.clear();
        AppConstants.webProductImages.clear();
        return true;
      },
      child: GestureDetector(
        onTap: () {
          CommonMethods.closeKeyboard(context);
        },
        child: Scaffold(
          // resizeToAvoidBottomInset: false,

          // resizeToAvoidBottomPadding: false,
          appBar: appBar(),
          backgroundColor: AppColors.appWhite,

          body: SafeArea(child: body()),
        ),
      ),
    );
  }

  // endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: AppStrings.addProduct,
        isDefaultMenuVisible: false,
        onTapLeading: () {
          addProductBloc.onTapBack();
        },
        isMembershipVisible: false,
        isCartVisible: false,
        isTextButtonVisible: true,
        textButtonWidget:
            AppCommonWidgets.appBarTextButtonText(text: AppStrings.next),
        onTapTextButton: () {
          addProductBloc.goToAddProductPreview();
        });
  }

  //endregion

  // region Body
  Widget body() {
    const double verticalSpace = 10;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: ListView(
        shrinkWrap: true,
        // mainAxisAlignment: MainAxisAlignment.start,
        // crossAxisAlignment: CrossAxisAlignment.start,
        // mainAxisSize: MainAxisSize.min,
        children: [
          //brandName(),
          addAndManageImages(),

          // addImage(),
          verticalSizedBox(verticalSpace),
          basicDetails(),
          verticalSizedBox(verticalSpace),
          inventoryOptions(),
          verticalSizedBox(verticalSpace),
          fulfillmentSetting(),
          verticalSizedBox(verticalSpace),
          returnSettings(),
          verticalSizedBox(verticalSpace),
          swadeshiLabels(),
          verticalSizedBox(verticalSpace),
          visibilityOption(),
          verticalSizedBox(verticalSpace),
          productPromotionsOption(),
          verticalSizedBox(verticalSpace),
          additionalInfo(),
          // addAStory(),
          // options(),
          AppCommonWidgets.bottomListSpace(context: context)
        ],
      ),
    );
  }

  // endregion

  //region Add Images

  Widget addImage() {
    return Align(
      alignment: Alignment.center,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          StreamBuilder<bool>(
              stream: addProductBloc.imageCtrl.stream,
              builder: (context, snapshot) {
                // For web platform
                if (kIsWeb) {
                  if (AppConstants.webProductImages.isNotEmpty) {
                    return InkWell(
                      onTap: () {
                        addProductBloc.goToSelectedImageScreen();
                      },
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(CommonMethods()
                            .getBorderRadius(
                                height: 75,
                                imageType: CustomImageContainerType.product)),
                        child: Container(
                            height: 75,
                            width: 75,
                            decoration: BoxDecoration(
                                color: AppColors.lightGray2,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(11))),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(
                                  CommonMethods().getBorderRadius(
                                      height: 75,
                                      imageType:
                                          CustomImageContainerType.product)),
                              // Display the first web image
                              child: Image.memory(
                                AppConstants.webProductImages[0]['bytes'],
                                fit: BoxFit.fill,
                                height: 200,
                                width: 200,
                              ),
                            )),
                      ),
                    );
                  }
                }
                // For mobile platform
                else if (AppConstants.multipleSelectedImage.isNotEmpty) {
                  return InkWell(
                    onTap: () {
                      addProductBloc.goToSelectedImageScreen();
                    },
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(CommonMethods()
                          .getBorderRadius(
                              height: 75,
                              imageType: CustomImageContainerType.product)),
                      child: Container(
                          height: 75,
                          width: 75,
                          decoration: BoxDecoration(
                              color: AppColors.lightGray2,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(11))),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(CommonMethods()
                                .getBorderRadius(
                                    height: 75,
                                    imageType:
                                        CustomImageContainerType.product)),
                            child: Image.file(
                              File(AppConstants.multipleSelectedImage[0].path),
                              fit: BoxFit.cover,
                              // cacheHeight: 200,
                              // cacheWidth: 200,
                            ),
                          )),
                    ),
                  );
                }

                // Default placeholder when no images are selected
                return ClipRRect(
                  borderRadius: BorderRadius.circular(CommonMethods()
                      .getBorderRadius(
                          height: 75,
                          imageType: CustomImageContainerType.product)),
                  child: Container(
                    height: 75,
                    width: 75,
                    decoration: BoxDecoration(
                        color: AppColors.textFieldFill2,
                        borderRadius: BorderRadius.all(Radius.circular(10))),
                    child: SvgPicture.asset(AppImages.productPlaceHolder),
                  ),
                );
              }),
          verticalSizedBox(24),
          Consumer<AppConfigDataModel>(
            builder: (BuildContext context, AppConfigDataModel value,
                Widget? child) {
              return CupertinoButton(
                  alignment: Alignment.center,
                  borderRadius: const BorderRadius.all(Radius.circular(9)),
                  color: AppColors.brandBlack,
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(AppImages.plus,
                          color: AppColors.appWhite, height: 16, width: 16),
                      horizontalSizedBox(10),
                      Text(
                        "${AppStrings.addImages} (up to ${value.appConfig!.productImageLimit})",
                        style: AppTextStyle.contentText0(
                            textColor: AppColors.appWhite),
                      )
                    ],
                  ),
                  onPressed: () {
                    addProductBloc.goToAddImage();
                  });
            },
          )
        ],
      ),
    );
  }

  //endregion

  //region Add and Manage Images

  Widget addAndManageImages() {
    const double imageHeight = 200;
    return Align(
      alignment: Alignment.center,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          StreamBuilder<bool>(
            stream: addProductBloc.imageCtrl.stream,
            builder: (context, snapshot) {
              // Web platform
              if (kIsWeb && AppConstants.webProductImages.isNotEmpty) {
                return InkWell(
                  onTap: () {
                    addProductBloc.goToSelectedImageScreen();
                  },
                  child: SizedBox(
                    height: imageHeight,
                    child: ReorderableListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: AppConstants.webProductImages.length,
                      itemBuilder: (context, index) {
                        return Container(
                          key: ValueKey(index),
                          margin: const EdgeInsets.only(right: 8),
                          child: Stack(
                            children: [
                              // Image container
                              ClipRRect(
                                borderRadius: BorderRadius.circular(
                                  CommonMethods().getBorderRadius(
                                    height: 75,
                                    imageType: CustomImageContainerType.product,
                                  ),
                                ),
                                child: Container(
                                  color: AppColors.lightGray2,
                                  child: PostAndProductImageWidgets(
                                    localOrNetworkImage: AppConstants
                                        .webProductImages[index]['bytes'],
                                    imageSize: imageHeight,
                                  ),
                                ),
                              ),

                              // Remove button
                              Positioned(
                                top: 4,
                                right: 4,
                                child: InkWell(
                                  onTap: () {
                                    setState(() {
                                      AppConstants.webProductImages
                                          .removeAt(index);
                                    });
                                  },
                                  child: SvgPicture.asset(
                                    AppImages.removeCircle,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                      onReorder: (oldIndex, newIndex) {
                        setState(() {
                          if (oldIndex < newIndex) {
                            newIndex -= 1;
                          }
                          final item =
                              AppConstants.webProductImages.removeAt(oldIndex);
                          AppConstants.webProductImages.insert(newIndex, item);
                        });
                      },
                    ),
                  ),
                );
              }

              // Mobile platform
              if (!kIsWeb && AppConstants.multipleSelectedImage.isNotEmpty) {
                return InkWell(
                  onTap: () {
                    addProductBloc.goToAddImage();
                  },
                  child: SizedBox(
                    height: imageHeight + 20,
                    child: ReorderableListView.builder(
                      // padding: const EdgeInsets.only(top: 20, bottom: 20),
                      scrollDirection: Axis.horizontal,
                      itemCount: AppConstants.multipleSelectedImage.length,
                      itemBuilder: (context, index) {
                        return Container(
                          key: ValueKey(
                              AppConstants.multipleSelectedImage[index]),
                          margin: const EdgeInsets.only(right: 8, top: 8),
                          child: Stack(
                            children: [
                              // Image container
                              ClipRRect(
                                borderRadius: BorderRadius.circular(
                                  CommonMethods().getBorderRadius(
                                    height: 75,
                                    imageType: CustomImageContainerType.product,
                                  ),
                                ),
                                child: Container(
                                  color: AppColors.lightGray2,
                                  child: PostAndProductImageWidgets(
                                    localOrNetworkImage: AppConstants
                                        .multipleSelectedImage[index].path,
                                    imageSize: imageHeight,
                                  ),
                                ),
                              ),

                              // Remove button
                              Positioned(
                                top: 4,
                                right: 4,
                                child: InkWell(
                                  onTap: () {
                                    setState(() {
                                      AppConstants.multipleSelectedImage
                                          .removeAt(index);
                                    });
                                  },
                                  child: SvgPicture.asset(
                                    AppImages.removeCircle,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                      onReorder: (oldIndex, newIndex) {
                        setState(() {
                          if (oldIndex < newIndex) {
                            newIndex -= 1;
                          }
                          final item = AppConstants.multipleSelectedImage
                              .removeAt(oldIndex);
                          AppConstants.multipleSelectedImage
                              .insert(newIndex, item);
                        });
                      },
                    ),
                  ),
                );
              }

              // Default placeholder when no images are selected
              return AspectRatio(
                aspectRatio: 16 / 9,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(
                    CommonMethods().getBorderRadius(
                      height: 75,
                      imageType: CustomImageContainerType.product,
                    ),
                  ),
                  child: Container(
                      decoration: BoxDecoration(
                        color: AppColors.textFieldFill2,
                        borderRadius:
                            const BorderRadius.all(Radius.circular(10)),
                      ),
                      child: InkWell(
                          onTap: () {
                            addProductBloc.goToAddImage();
                          },
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.add_photo_alternate_outlined,
                                size: 60,
                                color: AppColors.writingColor2,
                              ),
                              verticalSizedBox(8),
                              // Text(
                              //   'Add Product Images',
                              //   style: AppTextStyle.contentText0(
                              //       textColor: AppColors.writingColor2),
                              // ),
                              verticalSizedBox(4),
                              Text(
                                'Tap to upload product photos',
                                style: AppTextStyle.contentText1(
                                    textColor: AppColors.writingColor3),
                              ),
                            ],
                          ))),
                ),
              );
            },
          ),
          verticalSizedBox(10),
          Consumer<AppConfigDataModel>(
            builder: (BuildContext context, AppConfigDataModel value,
                Widget? child) {
              return Align(
                alignment: Alignment.center,
                child: InkWell(
                  onTap: () {
                    addProductBloc.goToAddImage();
                  },
                  child: Text(
                    AppConstants.multipleSelectedImage.length <
                            value.appConfig!.productImageLimit
                        ? 'Add upto ${value.appConfig!.productImageLimit} product photos • Hold & Drag to reorder' //${AppConstants.multipleSelectedImage.length} /
                        : '${value.appConfig!.productImageLimit} photos added • Hold & Drag to reorder',
                    style: AppTextStyle.smallTextRegular(
                        textColor: AppColors.writingColor2),
                  ),
                ),
              );
            },
          ),
          // Consumer<AppConfigDataModel>(
          //   builder: (BuildContext context, AppConfigDataModel value,
          //       Widget? child) {
          //     return CupertinoButton(
          //       alignment: Alignment.center,
          //       borderRadius: const BorderRadius.all(Radius.circular(9)),
          //       color: AppColors.brandBlack,
          //       padding: const EdgeInsets.symmetric(horizontal: 10),
          //       child: Row(
          //         mainAxisSize: MainAxisSize.min,
          //         crossAxisAlignment: CrossAxisAlignment.center,
          //         mainAxisAlignment: MainAxisAlignment.center,
          //         children: [
          //           SvgPicture.asset(AppImages.plus,
          //               color: AppColors.appWhite, height: 16, width: 16),
          //           horizontalSizedBox(10),
          //           Text(
          //             "${AppStrings.addImages} (up to ${value.appConfig!.productImageLimit})",
          //             style: AppTextStyle.contentText0(
          //               textColor: AppColors.appWhite,
          //             ),
          //           )
          //         ],
          //       ),
          //       onPressed: () {
          //         addProductBloc.goToAddImage();
          //       },
          //     );
          //   },
          // )
        ],
      ),
    );
  }

//endregion

// Add edit product Text fields
  Widget addEditProductTextFields() {
    return const AddEditProductFields();
  }
  //endregion

  //region Options
  Widget options() {
    return StreamBuilder<bool>(
        stream: addProductBloc.optionsRefreshCtrl.stream,
        builder: (context, snapshot) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              inventoryOptions(),
              // verticalSizedBox(24),
              swadeshiLabels(),
              // verticalSizedBox(24),
              fulfillmentSetting(),
              // verticalSizedBox(24),
              returnSettings(),
              // verticalSizedBox(24),
              // addAStory(),
            ],
          );
        });
  }
  //endregion

  //region Add labels
  Widget swadeshiLabels() {
    return StreamBuilder<bool>(
      stream: addProductBloc.optionsRefreshCtrl.stream,
      builder: (context, snapshot) {
        return AppCommonWidgets.settingOption(
          optionText: AppStrings.swadeshiLabels,
          onTap: () {
            addProductBloc.goToLabels(storeReference: widget.storeReference);
          },
          subtitle:
              'Select brand & product Swadeshi levels for visibility boost.',
          subtitleColor: AppColors.writingColor2,
          isArrowVisible: true,
          arrowColor: AppColors.appBlack,
          optionTextColor: AppColors.appBlack,
          isDoneVisible: addProductBloc.product.swadeshiBrand != null &&
              addProductBloc.product.swadeshiMade != null,
        );
      },
    );
  }
  //endregion

  //region Inventory Options
  Widget inventoryOptions() {
    return AppCommonWidgets.settingOption(
      optionText: AppStrings.inventoryAndPricing,
      onTap: () {
        addProductBloc.goToInventoryOptions(
            storeReference: widget.storeReference);
      },
      subtitle: 'Add variants (if any), set MRP, selling price, and stock.',
      subtitleColor: AppColors.writingColor2,
      isArrowVisible: true,
      arrowColor: AppColors.appBlack,
      optionTextColor: AppColors.appBlack,
      isDoneVisible: (addProductBloc.product.options != null &&
              addProductBloc.product.options!.isNotEmpty) ||
          (addProductBloc.product.variants != null &&
              addProductBloc.product.variants!.isNotEmpty),
    );
  }
  //endregion

  //region Delivery Setting
  Widget fulfillmentSetting() {
    return Consumer<StoreDashboardDataModel>(
      builder: (BuildContext context, data, Widget? child) {
        return AppCommonWidgets.settingOption(
          optionText: AppStrings.fulfillment,
          onTap: () {
            addProductBloc.goToDeliverSettingScreen();
          },
          subtitle:
              'Choose pickup location, shipping options, and delivery settings.',
          subtitleColor: AppColors.writingColor2,
          isArrowVisible: true,
          arrowColor: AppColors.appBlack,
          optionTextColor: AppColors.appBlack,
          isDoneVisible: data.storeDashBoard.deliverySettings! ||
              addProductBloc.addProductLevelDeliverySettingResponse
                      .deliverySettingData !=
                  null,
        );
      },
    );
  }
  //endregion

  //region Set Return settings
  Widget returnSettings() {
    return Consumer<StoreDashboardDataModel>(
      builder: (BuildContext context, data, Widget? child) {
        return AppCommonWidgets.settingOption(
          optionText: AppStrings.returnsAndRefunds,
          onTap: () {
            addProductBloc.goToReturnAndWarranty();
          },
          subtitle: 'Set return eligibility, return window, and refund rules.',
          subtitleColor: AppColors.writingColor2,
          isArrowVisible: true,
          arrowColor: AppColors.appBlack,
          optionTextColor: AppColors.appBlack,
          isDoneVisible: data.storeDashBoard.warrantyAndReturn! ||
              addProductBloc.addProductLevelReturnSettingresponse.data != null,
        );
      },
    );
  }
  //endregion

  //region Add a story
  // Widget addAStory(){
  //   return AddProductCommonWidgets.deliveryReturnButton(buttonName: AppStrings.tagStories, onPress: (){
  //     addProductBloc.goToTagStories();
  //   }, context: context,
  //       isDoneVisible: false
  //
  //   );
  // }
//endregion

  //region Add Product Text Fields Option
  Widget basicDetails() {
    return AppCommonWidgets.settingOption(
      optionText: "Basic Details",
      subtitle: "Brand, product name, category, and description.",
      subtitleColor: AppColors.writingColor3,
      onTap: () {
        goToBasicDetailsScreen();
      },
      isArrowVisible: true,
      arrowColor: AppColors.appBlack,
      optionTextColor: AppColors.appBlack,
      isDoneVisible: addProductBloc.isBasicDetailsFilled(),
    );
  }
  //endregion

  //region Go to Add Product Text Fields Screen
  void goToBasicDetailsScreen() async {
    var screen = const ProductTextFieldsScreen(
      isEditMode: false,
      section: ProductDetailsSection.basic,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    await Navigator.push(context, route);
    // Mark section visited and refresh the UI to update the done status
    addProductBloc.markVisitedBasicDetails();
    if (mounted) setState(() {});
  }
  //endregion

  //region Product Promotions Option
  Widget productPromotionsOption() {
    return AppCommonWidgets.settingOption(
      optionText: 'Promotions & Offers',
      onTap: () {
        goToProductPromotionsScreen();
      },
      subtitle: 'Set affiliate promotion or other offers.',
      subtitleColor: AppColors.writingColor2,
      isArrowVisible: true,
      arrowColor: AppColors.appBlack,
      optionTextColor: AppColors.appBlack,
      isDoneVisible: addProductBloc.isProductPromotionsFilled(),
      isOptional: true,
    );
  }

  void goToProductPromotionsScreen() async {
    var screen = const ProductTextFieldsScreen(
      isEditMode: false,
      section: ProductDetailsSection.promotions,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    await Navigator.push(context, route);
    // Mark section visited and refresh the UI to update the done status
    addProductBloc.markVisitedPromotions();
    if (mounted) setState(() {});
  }
  //endregion

  //region Visibility Option
  Widget visibilityOption() {
    return AppCommonWidgets.settingOption(
      optionText: 'Discovery & Tags',
      onTap: () {
        goToVisibilityScreen();
      },
      subtitle:
          'Product slug, code, hashtags, targeted gender — help buyers find it easily.',
      subtitleColor: AppColors.writingColor2,
      isArrowVisible: true,
      arrowColor: AppColors.appBlack,
      optionTextColor: AppColors.appBlack,
      isDoneVisible: addProductBloc.isVisibilityFilled(),
      isOptional: true,
    );
  }

  void goToVisibilityScreen() async {
    var screen = const ProductTextFieldsScreen(
      isEditMode: false,
      section: ProductDetailsSection.visibility,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    await Navigator.push(context, route);
    // Mark section visited and refresh the UI to update the done status
    addProductBloc.markVisitedVisibility();
    if (mounted) setState(() {});
  }
  //endregion

  //region More Details Option
  Widget additionalInfo() {
    return AppCommonWidgets.settingOption(
      optionText: 'Additional Info',
      onTap: () {
        goToMoreDetailsScreen();
      },
      subtitle:
          'All extra fields like promotion links and uncategorized details.',
      subtitleColor: AppColors.writingColor2,
      isArrowVisible: true,
      arrowColor: AppColors.appBlack,
      optionTextColor: AppColors.appBlack,
      isDoneVisible: addProductBloc.isMoreDetailsFilled(),
      isOptional: true,
    );
  }
  //endregion

  //region Go to More Details Screen
  void goToMoreDetailsScreen() async {
    var screen = const ProductTextFieldsScreen(
      isEditMode: false,
      section: ProductDetailsSection.moreDetails,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    await Navigator.push(context, route);
    // Mark section visited and refresh the UI to update the done status
    addProductBloc.markVisitedMoreDetails();
    if (mounted) setState(() {});
  }
  //endregion
}
