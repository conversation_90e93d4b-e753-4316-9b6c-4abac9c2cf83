import 'package:flutter/material.dart';
import 'package:swadesic/features/navigation/buyer_navigation.dart';
import 'package:swadesic/features/navigation/seller_navigation.dart';
import 'package:swadesic/util/app_constants.dart';

/// Navigation router that determines which navigation system to show
/// based on the user's current role (buyer vs seller)
class NavigationRouter extends StatefulWidget {
  final bool isFromOnboardingFlow;

  const NavigationRouter({
    super.key,
    this.isFromOnboardingFlow = false,
  });

  @override
  State<NavigationRouter> createState() => _NavigationRouterState();
}

class _NavigationRouterState extends State<NavigationRouter> {
  @override
  void initState() {
    super.initState();
    // Set the correct initial tab based on account type during app startup
    _setInitialTabBasedOnAccountType();

    // Mark navigation router as mounted
    print("FCM: NavigationRouter mounted");
    AppConstants.isNavigationRouterMounted = true;
    AppConstants.checkAndUpdateFullInitializationStatus();
  }

  void _setInitialTabBasedOnAccountType() {
    final isUserView = AppConstants.appData.isUserView ?? true;
    final isStoreView = AppConstants.appData.isStoreView ?? false;

    debugPrint(
        'NavigationRouter: isUserView=$isUserView, isStoreView=$isStoreView');

    if (isStoreView && !isUserView) {
      // Store account - default start up tab is Profile tab (index 5)
      AppConstants.storePersistentTabController.index = 5;
      debugPrint(
          'NavigationRouter: Set storePersistentTabController.index to 5 for store account');
    } else {
      // User account - default start up tab is Home tab (index 0)
      AppConstants.userPersistentTabController.index = 0;
      debugPrint(
          'NavigationRouter: Set userPersistentTabController.index to 0 for user account');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determine which navigation to show based on user role
    final isUserView = AppConstants.appData.isUserView ?? true;
    final isStoreView = AppConstants.appData.isStoreView ?? false;

    // Show seller navigation if user is in store view mode
    if (isStoreView && !isUserView) {
      return const SellerNavigation();
    }

    // Default to buyer navigation
    return BuyerNavigation(
      isFromOnboardingFlow: widget.isFromOnboardingFlow,
    );
  }
}

/// Legacy compatibility wrapper for UserBottomNavigation
/// This allows existing code to continue working while using the new navigation system
class UserBottomNavigation extends StatelessWidget {
  final bool isFromOnboardingFlow;

  const UserBottomNavigation({
    super.key,
    this.isFromOnboardingFlow = false,
  });

  @override
  Widget build(BuildContext context) {
    return NavigationRouter(
      isFromOnboardingFlow: isFromOnboardingFlow,
    );
  }
}

/// Legacy compatibility wrapper for StoreBottomNavigation
/// This allows existing code to continue working while using the new navigation system
class StoreBottomNavigation extends StatelessWidget {
  const StoreBottomNavigation({super.key});

  @override
  Widget build(BuildContext context) {
    return const NavigationRouter();
  }
}
